name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: asia-southeast1-docker.pkg.dev
  PROJECT_ID: emacs-464306
  REPOSITORY: prod/anandjewels

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: '1.23'
        
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'
        cache: 'npm'
        cache-dependency-path: |
          frontend/package-lock.json
          admin-portal/package-lock.json
    
    - name: Test Backend
      run: |
        cd backend
        go mod download
        go test ./...
    
    - name: Test Frontend
      run: |
        cd frontend
        npm ci
        npm test
    
    - name: Test Admin Portal
      run: |
        cd admin-portal
        npm ci
        npm test

  build:
    name: Build and Push Docker Images
    runs-on: ubuntu-latest
    needs: test
    if: github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}
        
    - name: Configure Docker to use gcloud as a credential helper
      run: |
        gcloud auth configure-docker ${{ env.REGISTRY }}
    
    - name: Generate short SHA
      id: vars
      run: echo "short_sha=$(echo ${{ github.sha }} | cut -c1-7)" >> $GITHUB_OUTPUT
    
    - name: Build and push Backend image
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        push: true
        tags: |
          ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/backend:${{ steps.vars.outputs.short_sha }}
          ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/backend:latest
        platforms: linux/amd64
        cache-from: type=gha
        cache-to: type=gha,mode=max
    
    - name: Build and push Frontend image
      uses: docker/build-push-action@v5
      with:
        context: ./frontend
        push: true
        tags: |
          ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/frontend:${{ steps.vars.outputs.short_sha }}
          ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/frontend:latest
        platforms: linux/amd64
        cache-from: type=gha
        cache-to: type=gha,mode=max
        build-args: |
          VITE_API_URL=http://backend-service:8080/api/v1
          VITE_APP_NAME=Anand Jewels
          VITE_DEBUG_MODE=false
    
    - name: Build and push Admin Portal image
      uses: docker/build-push-action@v5
      with:
        context: ./admin-portal
        push: true
        tags: |
          ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/admin-portal:${{ steps.vars.outputs.short_sha }}
          ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/admin-portal:latest
        platforms: linux/amd64
        cache-from: type=gha
        cache-to: type=gha,mode=max
        build-args: |
          VITE_API_URL=http://backend-service:8080/api/v1
          VITE_APP_NAME=Anand Jewels Admin
          VITE_DEBUG_MODE=false
    
    - name: Output image tags
      run: |
        echo "Built images with tags:"
        echo "Backend: ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/backend:${{ steps.vars.outputs.short_sha }}"
        echo "Frontend: ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/frontend:${{ steps.vars.outputs.short_sha }}"
        echo "Admin Portal: ${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/admin-portal:${{ steps.vars.outputs.short_sha }}"

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}
        
    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      
    - name: Get GKE credentials
      run: |
        gcloud container clusters get-credentials ${{ secrets.GKE_CLUSTER_NAME }} --zone ${{ secrets.GKE_ZONE }}
    
    - name: Generate short SHA
      id: vars
      run: echo "short_sha=$(echo ${{ github.sha }} | cut -c1-7)" >> $GITHUB_OUTPUT
    
    - name: Update Kubernetes manifests
      run: |
        sed -i "s|asia-southeast1-docker.pkg.dev/emacs-464306/prod/anandjewels/backend:.*|${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/backend:${{ steps.vars.outputs.short_sha }}|g" k8s/backend.yaml
        sed -i "s|asia-southeast1-docker.pkg.dev/emacs-464306/prod/anandjewels/frontend:.*|${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/frontend:${{ steps.vars.outputs.short_sha }}|g" k8s/frontend.yaml
        sed -i "s|asia-southeast1-docker.pkg.dev/emacs-464306/prod/anandjewels/admin-portal:.*|${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/admin-portal:${{ steps.vars.outputs.short_sha }}|g" k8s/admin-portal.yaml
    
    - name: Deploy to staging
      run: |
        kubectl apply -f k8s/ -n jewelry-store-staging

  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Authenticate to Google Cloud
      uses: google-github-actions/auth@v2
      with:
        credentials_json: ${{ secrets.GCP_SA_KEY }}
        
    - name: Set up Cloud SDK
      uses: google-github-actions/setup-gcloud@v2
      
    - name: Get GKE credentials
      run: |
        gcloud container clusters get-credentials ${{ secrets.GKE_CLUSTER_NAME }} --zone ${{ secrets.GKE_ZONE }}
    
    - name: Generate short SHA
      id: vars
      run: echo "short_sha=$(echo ${{ github.sha }} | cut -c1-7)" >> $GITHUB_OUTPUT
    
    - name: Update Kubernetes manifests
      run: |
        sed -i "s|asia-southeast1-docker.pkg.dev/emacs-464306/prod/anandjewels/backend:.*|${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/backend:${{ steps.vars.outputs.short_sha }}|g" k8s/backend.yaml
        sed -i "s|asia-southeast1-docker.pkg.dev/emacs-464306/prod/anandjewels/frontend:.*|${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/frontend:${{ steps.vars.outputs.short_sha }}|g" k8s/frontend.yaml
        sed -i "s|asia-southeast1-docker.pkg.dev/emacs-464306/prod/anandjewels/admin-portal:.*|${{ env.REGISTRY }}/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/admin-portal:${{ steps.vars.outputs.short_sha }}|g" k8s/admin-portal.yaml
    
    - name: Deploy to production
      run: |
        kubectl apply -f k8s/ -n jewelry-store
