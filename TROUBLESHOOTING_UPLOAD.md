# Image Upload Troubleshooting Guide

## Problem: Image upload failing with 404 error

### Symptoms
- <PERSON><PERSON><PERSON> requests to `/api/v1/upload/image` return 404 Not Found
- Frontend shows upload errors
- Browser network tab shows the endpoint doesn't exist

### Root Cause
The upload handler fails to initialize due to missing MinIO configuration, causing the upload routes to not be registered.

### Diagnosis Steps

#### 1. Check Upload Service Status
Visit the diagnostic endpoint:
```
GET https://your-backend-url.railway.app/api/v1/upload/status
```

If you get a 503 response with "upload_disabled", the upload service is not initialized.

#### 2. Check Backend Logs
In Railway dashboard:
1. Go to your backend service
2. Check the "Deployments" tab
3. Look for logs containing:
   ```
   Warning: Upload handler initialization failed: missing required MinIO configuration
   ```

#### 3. Verify Environment Variables
Check that all required MinIO environment variables are set in Railway:

**Required Variables:**
- `MINIO_ENDPOINT` - Your MinIO server endpoint
- `MINIO_ACCESS_KEY` - MinIO access key
- `MINIO_SECRET_KEY` - MinIO secret key  
- `MINIO_BUCKET_NAME` - Bucket name (e.g., "jewelry-images")
- `MINIO_USE_SSL` - Set to "true" for HTTPS endpoints

### Solutions

#### Option 1: Configure MinIO (Recommended)
Set up external MinIO or S3-compatible storage:

1. **Using AWS S3:**
   ```
   MINIO_ENDPOINT=s3.amazonaws.com
   MINIO_ACCESS_KEY=your-aws-access-key
   MINIO_SECRET_KEY=your-aws-secret-key
   MINIO_BUCKET_NAME=your-s3-bucket
   MINIO_USE_SSL=true
   ```

2. **Using MinIO Cloud:**
   ```
   MINIO_ENDPOINT=your-minio-cloud-endpoint
   MINIO_ACCESS_KEY=your-minio-access-key
   MINIO_SECRET_KEY=your-minio-secret-key
   MINIO_BUCKET_NAME=jewelry-images
   MINIO_USE_SSL=true
   ```

3. **Using DigitalOcean Spaces:**
   ```
   MINIO_ENDPOINT=nyc3.digitaloceanspaces.com
   MINIO_ACCESS_KEY=your-spaces-key
   MINIO_SECRET_KEY=your-spaces-secret
   MINIO_BUCKET_NAME=your-space-name
   MINIO_USE_SSL=true
   ```

#### Option 2: Use Alternative Storage
If you prefer not to use MinIO/S3, you can modify the upload handler to use:
- Cloudinary
- Firebase Storage
- Railway's volume storage (not recommended for production)

### Testing the Fix

1. **Set Environment Variables** in Railway dashboard
2. **Redeploy** the backend service
3. **Check Upload Health:**
   ```
   GET https://your-backend-url.railway.app/api/v1/upload/health
   ```
   Should return 200 OK with "healthy" status

4. **Test Upload:**
   ```bash
   curl -X POST \
     https://your-backend-url.railway.app/api/v1/upload/image \
     -F "file=@test-image.jpg"
   ```

### Prevention

1. **Use Environment Checker Script:**
   ```bash
   ./backend/scripts/check-env.sh
   ```

2. **Add Health Checks** to your deployment pipeline

3. **Monitor Upload Service** using the health endpoint

### Common Issues

#### Issue: "Access Denied" errors
- Check that MinIO credentials have proper permissions
- Verify bucket exists and is accessible
- Ensure bucket policy allows uploads

#### Issue: SSL/TLS errors
- Verify `MINIO_USE_SSL` setting matches your endpoint
- Check certificate validity for HTTPS endpoints

#### Issue: Network connectivity
- Ensure Railway can reach your MinIO endpoint
- Check firewall rules if using self-hosted MinIO

### Support

If you continue to have issues:
1. Check Railway logs for detailed error messages
2. Test MinIO connectivity from your local environment
3. Verify all environment variables are correctly set
4. Consider using the diagnostic endpoints for troubleshooting
