package tests

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"regexp"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
)

type ProductsTestSuite struct {
	TestSuite
}

func TestProductsTestSuite(t *testing.T) {
	suite.Run(t, new(ProductsTestSuite))
}

// Test data structures
type CreateProductRequest struct {
	SKU           string         `json:"sku"`
	Name          string         `json:"name"`
	Description   string         `json:"description"`
	Category      string         `json:"category"`
	Subcategory   *string        `json:"subcategory,omitempty"`
	Material      *string        `json:"material,omitempty"`
	MetalPurity   *string        `json:"metal_purity,omitempty"`
	Weight        *float64       `json:"weight,omitempty"`
	NetWeight     *float64       `json:"net_weight,omitempty"`
	Tags          []string       `json:"tags,omitempty"`
	Images        []ProductImage `json:"images,omitempty"`
	Availability  string         `json:"availability"`
	StockQuantity int            `json:"stock_quantity"`
}

type ProductImage struct {
	URL       string `json:"url"`
	Alt       string `json:"alt"`
	IsPrimary bool   `json:"is_primary"`
	SortOrder int    `json:"sort_order"`
}

func (suite *ProductsTestSuite) TestCreateProduct() {
	// Test data
	productID := uuid.New()
	product := CreateProductRequest{
		SKU:           "TEST-001",
		Name:          "Test Gold Ring",
		Description:   "A beautiful test gold ring",
		Category:      "rings",
		Material:      stringPtr("gold"),
		MetalPurity:   stringPtr("18k"),
		Weight:        float64Ptr(5.5),
		NetWeight:     float64Ptr(4.8),
		Tags:          []string{"gold", "ring", "jewelry"},
		Availability:  "available",
		StockQuantity: 10,
	}

	// Mock database expectations
	suite.MockDB.ExpectBegin()
	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`
		INSERT INTO products (id, sku, name, description, category, subcategory,
		weight, net_weight, material, metal_purity, availability, stock_quantity, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, NOW(), NOW())
		RETURNING id, created_at, updated_at
	`)).WithArgs(
		sqlmock.AnyArg(), // id
		product.SKU,
		product.Name,
		product.Description,
		product.Category,
		product.Subcategory,
		product.Weight,
		product.NetWeight,
		product.Material,
		product.MetalPurity,
		product.Availability,
		product.StockQuantity,
	).WillReturnRows(
		sqlmock.NewRows([]string{"id", "created_at", "updated_at"}).
			AddRow(productID, time.Now(), time.Now()),
	)

	// Mock tags insertion
	for _, tag := range product.Tags {
		suite.MockDB.ExpectExec(regexp.QuoteMeta(`
			INSERT INTO product_tags (product_id, tag) VALUES ($1, $2)
			ON CONFLICT (product_id, tag) DO NOTHING
		`)).WithArgs(productID, tag).WillReturnResult(sqlmock.NewResult(1, 1))
	}

	suite.MockDB.ExpectCommit()

	// Create request
	jsonData, _ := json.Marshal(product)
	req, _ := http.NewRequest("POST", "/api/v1/products", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Execute request
	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	// Assertions
	suite.Equal(http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal("Product created successfully", response["message"])
	suite.NotNil(response["product"])
}

func (suite *ProductsTestSuite) TestCreateProductValidationError() {
	// Test data with missing required fields
	product := CreateProductRequest{
		// Missing SKU and Name
		Description: "A test product",
		Category:    "rings",
	}

	// Create request
	jsonData, _ := json.Marshal(product)
	req, _ := http.NewRequest("POST", "/api/v1/products", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Execute request
	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	// Assertions
	suite.Equal(http.StatusBadRequest, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal("validation_error", response["error"])
}

func (suite *ProductsTestSuite) TestGetProducts() {
	// Mock database expectations
	rows := sqlmock.NewRows([]string{
		"id", "sku", "name", "description", "category", "subcategory",
		"weight", "net_weight", "material", "metal_purity", "availability", "stock_quantity",
		"min_stock_level", "tags", "is_featured", "is_active", "created_at", "updated_at",
	}).AddRow(
		uuid.New(), "TEST-001", "Test Gold Ring", "A beautiful test gold ring",
		"rings", nil, 5.5, 4.8, "gold", "18k",
		"available", 10, 0, "{}", false, true, time.Now(), time.Now(),
	)

	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`
		SELECT id, sku, name, description, category, subcategory,
		       weight, net_weight, material, metal_purity, availability,
		       stock_quantity, min_stock_level, tags, is_featured, is_active,
		       created_at, updated_at
		FROM products
		ORDER BY created_at DESC
		LIMIT $1 OFFSET $2
	`)).WithArgs(50, 0).WillReturnRows(rows)

	// Mock count query
	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`SELECT COUNT(*) FROM products`)).
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	// Create request
	req, _ := http.NewRequest("GET", "/api/v1/products", nil)

	// Execute request
	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	// Assertions
	suite.Equal(http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.NotNil(response["products"])
	suite.NotNil(response["pagination"])
}

func (suite *ProductsTestSuite) TestGetProduct() {
	productID := uuid.New()

	// Mock database expectations
	rows := sqlmock.NewRows([]string{
		"id", "sku", "name", "description", "category", "subcategory",
		"weight", "net_weight", "material", "metal_purity", "availability", "stock_quantity",
		"min_stock_level", "tags", "is_featured", "is_active", "created_at", "updated_at",
	}).AddRow(
		productID, "TEST-001", "Test Gold Ring", "A beautiful test gold ring",
		"rings", nil, 5.5, 4.8, "gold", "18k",
		"available", 10, 0, "{}", false, true, time.Now(), time.Now(),
	)

	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`
		SELECT id, sku, name, description, category, subcategory,
		       weight, net_weight, material, metal_purity, availability,
		       stock_quantity, min_stock_level, tags, is_featured, is_active,
		       created_at, updated_at
		FROM products WHERE id = $1
	`)).WithArgs(productID).WillReturnRows(rows)

	// Mock tags query
	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`
		SELECT tag FROM product_tags WHERE product_id = $1
	`)).WithArgs(productID).WillReturnRows(
		sqlmock.NewRows([]string{"tag"}).AddRow("gold").AddRow("ring"),
	)

	// Mock images query
	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`
		SELECT id, image_url, alt_text, display_order, is_primary, created_at, updated_at
		FROM product_images WHERE product_id = $1 ORDER BY display_order
	`)).WithArgs(productID).WillReturnRows(
		sqlmock.NewRows([]string{"id", "image_url", "alt_text", "display_order", "is_primary", "created_at", "updated_at"}),
	)

	// Create request
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/products/%s", productID), nil)

	// Execute request
	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	// Assertions
	suite.Equal(http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.NotNil(response["product"])
}

func (suite *ProductsTestSuite) TestGetProductNotFound() {
	productID := uuid.New()

	// Mock database expectations - no rows returned
	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`
		SELECT id, sku, name, description, category, subcategory,
		       weight, net_weight, material, metal_purity, availability,
		       stock_quantity, min_stock_level, tags, is_featured, is_active,
		       created_at, updated_at
		FROM products WHERE id = $1
	`)).WithArgs(productID).WillReturnRows(sqlmock.NewRows([]string{}))

	// Create request
	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/products/%s", productID), nil)

	// Execute request
	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	// Assertions
	suite.Equal(http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal("product_not_found", response["error"])
}

// Helper functions
func stringPtr(s string) *string {
	return &s
}

func float64Ptr(f float64) *float64 {
	return &f
}
