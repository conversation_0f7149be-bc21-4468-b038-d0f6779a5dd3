# Backend Tests

This directory contains comprehensive tests for the Jewelry Backend API.

## Test Structure

### Test Suites

1. **ProductsTestSuite** (`products_test.go`)
   - Tests product CRUD operations
   - Validates product creation, retrieval, updating, and deletion
   - Tests product validation and error handling

2. **UploadTestSuite** (`upload_test.go`)
   - Tests image upload functionality
   - Validates file upload to MinIO storage
   - Tests product image management
   - Includes file validation and error handling

3. **OrdersTestSuite** (`orders_test.go`)
   - Tests order management functionality
   - Validates order creation, retrieval, and status updates
   - Tests order item management
   - Includes order number lookup functionality

4. **CollectionsTestSuite** (`collections_test.go`)
   - Tests collection CRUD operations
   - Validates collection-product relationships
   - Tests collection sorting and management

5. **IntegrationTestSuite** (`integration_test.go`)
   - Tests API endpoints integration
   - Validates HTTP status codes and responses
   - Tests error handling and edge cases

### Test Setup

The test suite uses:
- **sqlmock** for database mocking
- **testify/suite** for organized test structure
- **Gin test mode** for HTTP testing
- **httptest** for HTTP request/response testing

## Running Tests

### Prerequisites

1. Install test dependencies:
```bash
cd backend
go mod tidy
```

2. Ensure MinIO is running (for upload tests):
```bash
podman compose up minio -d
```

### Test Commands

```bash
# Run all tests
make test

# Run tests with verbose output
make test-verbose

# Run tests with coverage report
make test-coverage

# Run only unit tests
make test-unit

# Run only integration tests
make test-integration

# Clean test cache
make clean
```

### Manual Test Execution

```bash
# Run all tests
go test ./tests/... -v

# Run specific test suite
go test ./tests/... -v -run "TestProductsTestSuite"

# Run specific test
go test ./tests/... -v -run "TestCreateProduct"

# Run with race detection
go test ./tests/... -v -race

# Run with coverage
go test ./tests/... -v -coverprofile=coverage.out
go tool cover -html=coverage.out -o coverage.html
```

## Test Coverage

The tests cover:

### API Endpoints
- ✅ `GET /api/v1/products` - List products
- ✅ `POST /api/v1/products` - Create product
- ✅ `GET /api/v1/products/:id` - Get product
- ✅ `PUT /api/v1/products/:id` - Update product
- ✅ `DELETE /api/v1/products/:id` - Delete product
- ✅ `POST /api/v1/products/:id/images` - Upload product image
- ✅ `DELETE /api/v1/products/:id/images/:image_id` - Delete product image
- ✅ `POST /api/v1/upload/image` - Upload general image
- ✅ `GET /api/v1/collections` - List collections
- ✅ `POST /api/v1/collections` - Create collection
- ✅ `GET /api/v1/collections/:id` - Get collection
- ✅ `PUT /api/v1/collections/:id` - Update collection
- ✅ `DELETE /api/v1/collections/:id` - Delete collection
- ✅ `GET /api/v1/collections/:id/products` - Get collection products
- ✅ `POST /api/v1/collections/:id/products` - Add product to collection
- ✅ `DELETE /api/v1/collections/:id/products/:product_id` - Remove product from collection
- ✅ `GET /api/v1/orders` - List orders
- ✅ `POST /api/v1/orders` - Create order
- ✅ `GET /api/v1/orders/:id` - Get order
- ✅ `GET /api/v1/orders/number/:order_number` - Get order by number
- ✅ `GET /api/v1/health` - Health check

### Validation & Error Handling
- ✅ Input validation for all endpoints
- ✅ UUID validation
- ✅ File upload validation
- ✅ Database error handling
- ✅ Not found scenarios
- ✅ Invalid request handling

### Database Operations
- ✅ CRUD operations for all entities
- ✅ Transaction handling
- ✅ Foreign key relationships
- ✅ Data integrity constraints

### File Upload & Storage
- ✅ Image upload to MinIO
- ✅ File validation (type, size)
- ✅ Image variant generation
- ✅ Storage cleanup on errors

## Test Data

Tests use:
- **Mock database** with sqlmock for predictable data
- **Generated UUIDs** for entity IDs
- **Realistic test data** for jewelry products
- **Minimal test images** for upload testing

## Continuous Integration

The test suite is designed to run in CI/CD environments:

1. **No external dependencies** (uses mocks)
2. **Fast execution** (unit tests run in milliseconds)
3. **Comprehensive coverage** (all major code paths)
4. **Clear error messages** (detailed test failures)

## Troubleshooting

### Common Issues

1. **MinIO Connection Errors**
   - Ensure MinIO is running: `podman compose up minio -d`
   - Check MinIO health: `curl http://localhost:9000/minio/health/live`

2. **Database Mock Errors**
   - Ensure all mock expectations are set correctly
   - Check that mock expectations match actual queries

3. **Test Timeouts**
   - Increase timeout for slow operations
   - Check for deadlocks in concurrent tests

### Debug Mode

Run tests with debug output:
```bash
go test ./tests/... -v -args -debug
```

## Contributing

When adding new tests:

1. Follow the existing test structure
2. Use descriptive test names
3. Mock external dependencies
4. Test both success and error cases
5. Update this README with new test coverage
