package tests

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"regexp"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
)

type OrdersTestSuite struct {
	TestSuite
}

func TestOrdersTestSuite(t *testing.T) {
	suite.Run(t, new(OrdersTestSuite))
}

// Test data structures
type CreateOrderRequest struct {
	CustomerName        string      `json:"customer_name"`
	CustomerPhone       string      `json:"customer_phone"`
	CustomerEmail       *string     `json:"customer_email,omitempty"`
	CustomerAddress     *Address    `json:"customer_address,omitempty"`
	Items               []OrderItem `json:"items"`
	Subtotal            float64     `json:"subtotal"`
	TaxAmount           float64     `json:"tax_amount"`
	TotalAmount         float64     `json:"total_amount"`
	Currency            string      `json:"currency"`
	PaymentMethod       string      `json:"payment_method"`
	PaymentStatus       string      `json:"payment_status"`
	Status              string      `json:"status"`
	SpecialInstructions *string     `json:"special_instructions,omitempty"`
}

type Address struct {
	Street     string `json:"street"`
	City       string `json:"city"`
	State      string `json:"state"`
	PostalCode string `json:"postal_code"`
	Country    string `json:"country"`
}

type OrderItem struct {
	ProductID string  `json:"product_id"`
	Quantity  int     `json:"quantity"`
	UnitPrice float64 `json:"unit_price"`
}

func (suite *OrdersTestSuite) TestCreateOrder() {
	orderID := uuid.New()
	productID := uuid.New()

	order := CreateOrderRequest{
		CustomerName:  "John Doe",
		CustomerPhone: "+91 98765 43210",
		CustomerEmail: stringPtr("<EMAIL>"),
		CustomerAddress: &Address{
			Street:     "123 Main St",
			City:       "Mumbai",
			State:      "Maharashtra",
			PostalCode: "400001",
			Country:    "India",
		},
		Items: []OrderItem{
			{
				ProductID: productID.String(),
				Quantity:  1,
				UnitPrice: 25000.00,
			},
		},
		Subtotal:            25000.00,
		TaxAmount:           0.00,
		TotalAmount:         25000.00,
		Currency:            "INR",
		PaymentMethod:       "phone_confirmation",
		PaymentStatus:       "pending",
		Status:              "pending",
		SpecialInstructions: stringPtr("Please handle with care"),
	}

	// Mock database expectations
	suite.MockDB.ExpectBegin()

	// Mock order insertion
	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`
		INSERT INTO orders (id, order_number, customer_name, customer_phone, customer_email, 
		customer_address, subtotal, tax_amount, total_amount, currency, payment_method, 
		payment_status, status, special_instructions, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, NOW(), NOW())
		RETURNING id, order_number, created_at, updated_at
	`)).WithArgs(
		sqlmock.AnyArg(), // id
		sqlmock.AnyArg(), // order_number
		order.CustomerName,
		order.CustomerPhone,
		order.CustomerEmail,
		sqlmock.AnyArg(), // customer_address JSON
		order.Subtotal,
		order.TaxAmount,
		order.TotalAmount,
		order.Currency,
		order.PaymentMethod,
		order.PaymentStatus,
		order.Status,
		order.SpecialInstructions,
	).WillReturnRows(
		sqlmock.NewRows([]string{"id", "order_number", "created_at", "updated_at"}).
			AddRow(orderID, "ORD-2024-001", time.Now(), time.Now()),
	)

	// Mock order items insertion
	for _, item := range order.Items {
		suite.MockDB.ExpectQuery(regexp.QuoteMeta(`
			INSERT INTO order_items (id, order_id, product_id, product_name, product_sku, 
			quantity, unit_price, total_price, created_at, updated_at)
			SELECT $1, $2, p.id, p.name, p.sku, $3, $4, $5, NOW(), NOW()
			FROM products p WHERE p.id = $6
			RETURNING id, product_name, product_sku
		`)).WithArgs(
			sqlmock.AnyArg(), // item id
			orderID,
			item.Quantity,
			item.UnitPrice,
			item.UnitPrice*float64(item.Quantity),
			item.ProductID,
		).WillReturnRows(
			sqlmock.NewRows([]string{"id", "product_name", "product_sku"}).
				AddRow(uuid.New(), "Test Product", "TEST-001"),
		)
	}

	suite.MockDB.ExpectCommit()

	// Create request
	jsonData, _ := json.Marshal(order)
	req, _ := http.NewRequest("POST", "/api/v1/orders", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Execute request
	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	// Assertions
	suite.Equal(http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal("Order created successfully", response["message"])
	suite.NotNil(response["order"])
}

func (suite *OrdersTestSuite) TestCreateOrderValidationError() {
	// Test with missing required fields
	order := CreateOrderRequest{
		// Missing customer name and phone
		Items: []OrderItem{
			{
				ProductID: uuid.New().String(),
				Quantity:  1,
				UnitPrice: 25000.00,
			},
		},
		TotalAmount: 25000.00,
	}

	jsonData, _ := json.Marshal(order)
	req, _ := http.NewRequest("POST", "/api/v1/orders", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	suite.Equal(http.StatusBadRequest, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal("validation_error", response["error"])
}

func (suite *OrdersTestSuite) TestGetOrders() {
	orderID := uuid.New()

	// Mock database expectations
	rows := sqlmock.NewRows([]string{
		"id", "order_number", "customer_name", "customer_phone", "customer_email",
		"customer_address", "subtotal", "tax_amount", "total_amount", "currency",
		"payment_method", "payment_status", "status", "special_instructions",
		"created_at", "updated_at",
	}).AddRow(
		orderID, "ORD-2024-001", "John Doe", "+91 98765 43210", "<EMAIL>",
		`{"street":"123 Main St","city":"Mumbai","state":"Maharashtra","postal_code":"400001","country":"India"}`,
		25000.00, 0.00, 25000.00, "INR", "phone_confirmation", "pending", "pending",
		"Please handle with care", time.Now(), time.Now(),
	)

	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`
		SELECT id, order_number, customer_name, customer_phone, customer_email,
		       customer_address, subtotal, tax_amount, total_amount, currency,
		       payment_method, payment_status, status, special_instructions,
		       created_at, updated_at
		FROM orders
		ORDER BY created_at DESC
		LIMIT $1 OFFSET $2
	`)).WithArgs(50, 0).WillReturnRows(rows)

	// Mock count query
	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`SELECT COUNT(*) FROM orders`)).
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	req, _ := http.NewRequest("GET", "/api/v1/orders", nil)

	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.NotNil(response["orders"])
	suite.NotNil(response["pagination"])
}

func (suite *OrdersTestSuite) TestGetOrder() {
	orderID := uuid.New()
	itemID := uuid.New()

	// Mock order query
	orderRows := sqlmock.NewRows([]string{
		"id", "order_number", "customer_name", "customer_phone", "customer_email",
		"customer_address", "subtotal", "tax_amount", "total_amount", "currency",
		"payment_method", "payment_status", "status", "special_instructions",
		"created_at", "updated_at",
	}).AddRow(
		orderID, "ORD-2024-001", "John Doe", "+91 98765 43210", "<EMAIL>",
		`{"street":"123 Main St","city":"Mumbai","state":"Maharashtra","postal_code":"400001","country":"India"}`,
		25000.00, 0.00, 25000.00, "INR", "phone_confirmation", "pending", "pending",
		"Please handle with care", time.Now(), time.Now(),
	)

	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`
		SELECT id, order_number, customer_name, customer_phone, customer_email,
		       customer_address, subtotal, tax_amount, total_amount, currency,
		       payment_method, payment_status, status, special_instructions,
		       created_at, updated_at
		FROM orders WHERE id = $1
	`)).WithArgs(orderID).WillReturnRows(orderRows)

	// Mock order items query
	itemRows := sqlmock.NewRows([]string{
		"id", "product_id", "product_name", "product_sku", "quantity", "unit_price", "total_price",
	}).AddRow(
		itemID, uuid.New(), "Test Product", "TEST-001", 1, 25000.00, 25000.00,
	)

	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`
		SELECT id, product_id, product_name, product_sku, quantity, unit_price, total_price
		FROM order_items WHERE order_id = $1
	`)).WithArgs(orderID).WillReturnRows(itemRows)

	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/orders/%s", orderID), nil)

	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.NotNil(response["order"])
}

// TestGetOrderByNumber - Commented out as this endpoint doesn't exist yet
// func (suite *OrdersTestSuite) TestGetOrderByNumber() { ... }

func (suite *OrdersTestSuite) TestGetOrderNotFound() {
	orderID := uuid.New()

	// Mock database expectations - no rows returned
	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`
		SELECT id, order_number, customer_name, customer_phone, customer_email,
		       customer_address, subtotal, tax_amount, total_amount, currency,
		       payment_method, payment_status, status, special_instructions,
		       created_at, updated_at
		FROM orders WHERE id = $1
	`)).WithArgs(orderID).WillReturnRows(sqlmock.NewRows([]string{}))

	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/orders/%s", orderID), nil)

	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	suite.Equal(http.StatusNotFound, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal("order_not_found", response["error"])
}
