package tests

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"regexp"
	"testing"
	"time"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
)

type CollectionsTestSuite struct {
	TestSuite
}

func TestCollectionsTestSuite(t *testing.T) {
	suite.Run(t, new(CollectionsTestSuite))
}

// Test data structures
type CreateCollectionRequest struct {
	Name        string  `json:"name"`
	Description *string `json:"description,omitempty"`
	IsActive    bool    `json:"is_active"`
	SortOrder   int     `json:"sort_order"`
}

func (suite *CollectionsTestSuite) TestCreateCollection() {
	collectionID := uuid.New()

	collection := CreateCollectionRequest{
		Name:        "Gold Rings Collection",
		Description: stringPtr("Beautiful collection of gold rings"),
		IsActive:    true,
		SortOrder:   1,
	}

	// Mock database expectations
	suite.MockDB.ExpectBegin()
	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`
		INSERT INTO collections (id, name, description, is_active, sort_order, created_at, updated_at)
		VALUES ($1, $2, $3, $4, $5, NOW(), NOW())
		RETURNING id, created_at, updated_at
	`)).WithArgs(
		sqlmock.AnyArg(), // id
		collection.Name,
		collection.Description,
		collection.IsActive,
		collection.SortOrder,
	).WillReturnRows(
		sqlmock.NewRows([]string{"id", "created_at", "updated_at"}).
			AddRow(collectionID, time.Now(), time.Now()),
	)
	suite.MockDB.ExpectCommit()

	// Create request
	jsonData, _ := json.Marshal(collection)
	req, _ := http.NewRequest("POST", "/api/v1/collections", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	// Execute request
	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	// Assertions
	suite.Equal(http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal("Collection created successfully", response["message"])
	suite.NotNil(response["collection"])
}

func (suite *CollectionsTestSuite) TestCreateCollectionValidationError() {
	// Test with missing required fields
	collection := CreateCollectionRequest{
		// Missing name
		Description: stringPtr("A test collection"),
		IsActive:    true,
	}

	jsonData, _ := json.Marshal(collection)
	req, _ := http.NewRequest("POST", "/api/v1/collections", bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	suite.Equal(http.StatusBadRequest, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal("validation_error", response["error"])
}

func (suite *CollectionsTestSuite) TestGetCollections() {
	collectionID := uuid.New()

	// Mock database expectations
	rows := sqlmock.NewRows([]string{
		"id", "name", "description", "is_active", "sort_order", "created_at", "updated_at",
	}).AddRow(
		collectionID, "Gold Rings Collection", "Beautiful collection of gold rings",
		true, 1, time.Now(), time.Now(),
	)

	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`
		SELECT id, name, description, is_active, sort_order, created_at, updated_at
		FROM collections
		ORDER BY sort_order ASC, created_at DESC
		LIMIT $1 OFFSET $2
	`)).WithArgs(50, 0).WillReturnRows(rows)

	// Mock count query
	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`SELECT COUNT(*) FROM collections`)).
		WillReturnRows(sqlmock.NewRows([]string{"count"}).AddRow(1))

	req, _ := http.NewRequest("GET", "/api/v1/collections", nil)

	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.NotNil(response["collections"])
	suite.NotNil(response["pagination"])
}

func (suite *CollectionsTestSuite) TestGetCollection() {
	collectionID := uuid.New()

	// Mock database expectations
	rows := sqlmock.NewRows([]string{
		"id", "name", "description", "is_active", "sort_order", "created_at", "updated_at",
	}).AddRow(
		collectionID, "Gold Rings Collection", "Beautiful collection of gold rings",
		true, 1, time.Now(), time.Now(),
	)

	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`
		SELECT id, name, description, is_active, sort_order, created_at, updated_at
		FROM collections WHERE id = $1
	`)).WithArgs(collectionID).WillReturnRows(rows)

	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/collections/%s", collectionID), nil)

	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.NotNil(response["collection"])
}

func (suite *CollectionsTestSuite) TestGetCollectionWithProducts() {
	collectionID := uuid.New()
	productID := uuid.New()

	// Mock collection existence check
	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`
		SELECT id FROM collections WHERE id = $1
	`)).WithArgs(collectionID).WillReturnRows(
		sqlmock.NewRows([]string{"id"}).AddRow(collectionID),
	)

	// Mock products query
	rows := sqlmock.NewRows([]string{
		"id", "sku", "name", "description", "price", "cost_price", "category", "subcategory",
		"material", "metal_purity", "weight", "dimensions", "availability", "stock_quantity",
		"created_at", "updated_at",
	}).AddRow(
		productID, "RING-001", "Gold Ring", "Beautiful gold ring",
		25000.00, nil, "rings", nil, "gold", "18k", 5.5, nil,
		"available", 10, time.Now(), time.Now(),
	)

	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`
		SELECT p.id, p.sku, p.name, p.description, p.price, p.cost_price, p.category, p.subcategory,
		       p.material, p.metal_purity, p.weight, p.dimensions, p.availability, p.stock_quantity,
		       p.created_at, p.updated_at
		FROM products p
		INNER JOIN collection_products cp ON p.id = cp.product_id
		WHERE cp.collection_id = $1
		ORDER BY cp.sort_order ASC, p.created_at DESC
		LIMIT $2 OFFSET $3
	`)).WithArgs(collectionID, 50, 0).WillReturnRows(rows)

	// Mock count query
	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`
		SELECT COUNT(*)
		FROM products p
		INNER JOIN collection_products cp ON p.id = cp.product_id
		WHERE cp.collection_id = $1
	`)).WithArgs(collectionID).WillReturnRows(
		sqlmock.NewRows([]string{"count"}).AddRow(1),
	)

	req, _ := http.NewRequest("GET", fmt.Sprintf("/api/v1/collections/%s/products", collectionID), nil)

	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.NotNil(response["products"])
	suite.NotNil(response["pagination"])
}

func (suite *CollectionsTestSuite) TestAddProductToCollection() {
	collectionID := uuid.New()
	productID := uuid.New()

	requestBody := map[string]interface{}{
		"product_id": productID.String(),
		"sort_order": 1,
	}

	// Mock collection existence check
	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`
		SELECT id FROM collections WHERE id = $1
	`)).WithArgs(collectionID).WillReturnRows(
		sqlmock.NewRows([]string{"id"}).AddRow(collectionID),
	)

	// Mock product existence check
	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`
		SELECT id FROM products WHERE id = $1
	`)).WithArgs(productID).WillReturnRows(
		sqlmock.NewRows([]string{"id"}).AddRow(productID),
	)

	// Mock insertion
	suite.MockDB.ExpectBegin()
	suite.MockDB.ExpectExec(regexp.QuoteMeta(`
		INSERT INTO collection_products (collection_id, product_id, sort_order, created_at, updated_at)
		VALUES ($1, $2, $3, NOW(), NOW())
		ON CONFLICT (collection_id, product_id) DO UPDATE SET
		sort_order = EXCLUDED.sort_order, updated_at = NOW()
	`)).WithArgs(collectionID, productID, 1).WillReturnResult(sqlmock.NewResult(1, 1))
	suite.MockDB.ExpectCommit()

	jsonData, _ := json.Marshal(requestBody)
	req, _ := http.NewRequest("POST", fmt.Sprintf("/api/v1/collections/%s/products", collectionID), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal("Product added to collection successfully", response["message"])
}

func (suite *CollectionsTestSuite) TestRemoveProductFromCollection() {
	collectionID := uuid.New()
	productID := uuid.New()

	// Mock collection existence check
	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`
		SELECT id FROM collections WHERE id = $1
	`)).WithArgs(collectionID).WillReturnRows(
		sqlmock.NewRows([]string{"id"}).AddRow(collectionID),
	)

	// Mock deletion
	suite.MockDB.ExpectBegin()
	suite.MockDB.ExpectExec(regexp.QuoteMeta(`
		DELETE FROM collection_products 
		WHERE collection_id = $1 AND product_id = $2
	`)).WithArgs(collectionID, productID).WillReturnResult(sqlmock.NewResult(1, 1))
	suite.MockDB.ExpectCommit()

	req, _ := http.NewRequest("DELETE", fmt.Sprintf("/api/v1/collections/%s/products/%s", collectionID, productID), nil)

	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal("Product removed from collection successfully", response["message"])
}

func (suite *CollectionsTestSuite) TestUpdateCollection() {
	collectionID := uuid.New()

	updateData := CreateCollectionRequest{
		Name:        "Updated Gold Rings Collection",
		Description: stringPtr("Updated description"),
		IsActive:    false,
		SortOrder:   2,
	}

	// Mock collection existence check
	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`
		SELECT id FROM collections WHERE id = $1
	`)).WithArgs(collectionID).WillReturnRows(
		sqlmock.NewRows([]string{"id"}).AddRow(collectionID),
	)

	// Mock update
	suite.MockDB.ExpectBegin()
	suite.MockDB.ExpectExec(regexp.QuoteMeta(`
		UPDATE collections SET name = $1, description = $2, is_active = $3, sort_order = $4, updated_at = NOW()
		WHERE id = $5
	`)).WithArgs(
		updateData.Name,
		updateData.Description,
		updateData.IsActive,
		updateData.SortOrder,
		collectionID,
	).WillReturnResult(sqlmock.NewResult(1, 1))
	suite.MockDB.ExpectCommit()

	jsonData, _ := json.Marshal(updateData)
	req, _ := http.NewRequest("PUT", fmt.Sprintf("/api/v1/collections/%s", collectionID), bytes.NewBuffer(jsonData))
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal("Collection updated successfully", response["message"])
}

func (suite *CollectionsTestSuite) TestDeleteCollection() {
	collectionID := uuid.New()

	// Mock collection existence check
	suite.MockDB.ExpectQuery(regexp.QuoteMeta(`
		SELECT id FROM collections WHERE id = $1
	`)).WithArgs(collectionID).WillReturnRows(
		sqlmock.NewRows([]string{"id"}).AddRow(collectionID),
	)

	// Mock deletion
	suite.MockDB.ExpectBegin()
	suite.MockDB.ExpectExec(regexp.QuoteMeta(`
		DELETE FROM collections WHERE id = $1
	`)).WithArgs(collectionID).WillReturnResult(sqlmock.NewResult(1, 1))
	suite.MockDB.ExpectCommit()

	req, _ := http.NewRequest("DELETE", fmt.Sprintf("/api/v1/collections/%s", collectionID), nil)

	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal("Collection deleted successfully", response["message"])
}
