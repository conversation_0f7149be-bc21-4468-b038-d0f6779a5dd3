package tests

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/stretchr/testify/suite"
)

type IntegrationTestSuite struct {
	TestSuite
}

func TestIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(IntegrationTestSuite))
}

func (suite *IntegrationTestSuite) TestHealthEndpoint() {
	req, _ := http.NewRequest("GET", "/api/v1/health", nil)

	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	suite.Equal(http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal("ok", response["status"])
}

func (suite *IntegrationTestSuite) TestCORSHeaders() {
	req, _ := http.NewRequest("OPTIONS", "/api/v1/health", nil)
	req.Header.Set("Origin", "http://localhost:3000")
	req.Header.Set("Access-Control-Request-Method", "GET")

	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	// Note: CORS middleware might not be set up in test environment
	// This test verifies the endpoint responds to OPTIONS requests
	suite.True(w.Code == http.StatusOK || w.Code == http.StatusNoContent)
}

func (suite *IntegrationTestSuite) TestInvalidEndpoint() {
	req, _ := http.NewRequest("GET", "/api/v1/nonexistent", nil)

	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	suite.Equal(http.StatusNotFound, w.Code)
}

func (suite *IntegrationTestSuite) TestInvalidMethod() {
	req, _ := http.NewRequest("PATCH", "/api/v1/health", nil)

	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	suite.Equal(http.StatusMethodNotAllowed, w.Code)
}

func (suite *IntegrationTestSuite) TestInvalidJSON() {
	req, _ := http.NewRequest("POST", "/api/v1/products", nil)
	req.Header.Set("Content-Type", "application/json")

	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	// Should return bad request for invalid JSON
	suite.Equal(http.StatusBadRequest, w.Code)
}

func (suite *IntegrationTestSuite) TestInvalidUUID() {
	req, _ := http.NewRequest("GET", "/api/v1/products/invalid-uuid", nil)

	w := httptest.NewRecorder()
	suite.Router.ServeHTTP(w, req)

	suite.Equal(http.StatusBadRequest, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	suite.NoError(err)
	suite.Equal("invalid_id", response["error"])
}
