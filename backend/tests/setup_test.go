package tests

import (
	"database/sql"
	"log"
	"os"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/anandjewels/jewelry-backend/internal/database"
	"github.com/anandjewels/jewelry-backend/internal/handlers"
	"github.com/anandjewels/jewelry-backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/suite"
)

// TestSuite provides a base test suite with common setup
type TestSuite struct {
	suite.Suite
	DB             *sql.DB
	MockDB         sqlmock.Sqlmock
	Router         *gin.Engine
	StorageService *services.StorageService
}

// SetupSuite runs once before all tests in the suite
func (suite *TestSuite) SetupSuite() {
	// Set Gin to test mode
	gin.SetMode(gin.TestMode)

	// Create mock database
	db, mock, err := sqlmock.New()
	if err != nil {
		log.Fatalf("Failed to create mock database: %v", err)
	}

	suite.DB = db
	suite.MockDB = mock

	// Set test environment variables
	os.Setenv("MINIO_ENDPOINT", "localhost:9000")
	os.Setenv("MINIO_ACCESS_KEY", "minioadmin")
	os.Setenv("MINIO_SECRET_KEY", "minioadmin123")
	os.Setenv("MINIO_BUCKET", "test-jewelry-images")
	os.Setenv("MINIO_USE_SSL", "false")
}

// TearDownSuite runs once after all tests in the suite
func (suite *TestSuite) TearDownSuite() {
	if suite.DB != nil {
		suite.DB.Close()
	}
}

// SetupTest runs before each test
func (suite *TestSuite) SetupTest() {
	// Create a new router for each test
	suite.Router = gin.New()

	// Create database wrapper
	dbWrapper := &database.DB{
		Postgres: suite.DB,
		Redis:    nil, // Redis not needed for tests
	}

	// Create storage service (will use test MinIO)
	storageService, err := services.NewStorageService()
	if err != nil {
		// For tests that don't need actual MinIO, we'll mock this
		suite.T().Logf("Warning: Could not create storage service: %v", err)
	}
	suite.StorageService = storageService

	// Setup handlers with mock database
	setupTestRoutes(suite.Router, dbWrapper, storageService)
}

// TearDownTest runs after each test
func (suite *TestSuite) TearDownTest() {
	// Ensure all expectations were met
	if err := suite.MockDB.ExpectationsWereMet(); err != nil {
		suite.T().Errorf("Unfulfilled mock expectations: %v", err)
	}
}

// setupTestRoutes configures routes for testing
func setupTestRoutes(router *gin.Engine, db *database.DB, storage *services.StorageService) {
	api := router.Group("/api/v1")

	// Create handlers
	productHandler := handlers.NewProductHandler(db)
	collectionHandler := handlers.NewCollectionHandler(db)
	orderHandler := handlers.NewOrderHandler(db)

	var uploadHandler *handlers.UploadHandler
	if storage != nil {
		var err error
		uploadHandler, err = handlers.NewUploadHandler(db)
		if err != nil {
			// Skip upload tests if MinIO is not available
			// suite.T().Logf("Warning: Could not create upload handler: %v", err)
		}
	}

	// Setup routes
	products := api.Group("/products")
	{
		products.GET("", productHandler.GetProducts)
		products.POST("", productHandler.CreateProduct)
		products.GET("/:id", productHandler.GetProduct)
		products.PUT("/:id", productHandler.UpdateProduct)
		products.DELETE("/:id", productHandler.DeleteProduct)

		if uploadHandler != nil {
			products.POST("/:id/images", uploadHandler.UploadProductImage)
			products.DELETE("/:id/images/:image_id", uploadHandler.DeleteProductImage)
		}
	}

	collections := api.Group("/collections")
	{
		collections.GET("", collectionHandler.GetCollections)
		collections.POST("", collectionHandler.CreateCollection)
		collections.GET("/:id", collectionHandler.GetCollection)
		collections.PUT("/:id", collectionHandler.UpdateCollection)
		collections.DELETE("/:id", collectionHandler.DeleteCollection)
		collections.GET("/:id/products", collectionHandler.GetCollectionWithProducts)
		collections.POST("/:id/products", collectionHandler.AddProductToCollection)
		collections.DELETE("/:id/products/:product_id", collectionHandler.RemoveProductFromCollection)
	}

	orders := api.Group("/orders")
	{
		orders.GET("", orderHandler.GetOrders)
		orders.POST("", orderHandler.CreateOrder)
		orders.GET("/:id", orderHandler.GetOrder)
		orders.PUT("/:id/status", orderHandler.UpdateOrderStatus)
	}

	// Only include handlers that exist
	// customers := api.Group("/customers")
	// inventory := api.Group("/inventory")

	if uploadHandler != nil {
		upload := api.Group("/upload")
		{
			upload.POST("/image", uploadHandler.UploadImage)
			// upload.DELETE("/image/:filename", uploadHandler.DeleteImage) // Method doesn't exist
		}
	}

	// Health check endpoint
	api.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{"status": "ok"})
	})
}

// Helper function to run test suites
func TestMain(m *testing.M) {
	// Setup
	code := m.Run()

	// Teardown
	os.Exit(code)
}
