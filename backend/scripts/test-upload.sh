#!/bin/bash

# Test script for image upload functionality
# Usage: ./test-upload.sh [backend-url] [image-file]

BACKEND_URL=${1:-"http://localhost:8080"}
IMAGE_FILE=${2:-""}

echo "🧪 Testing Image Upload Functionality"
echo "====================================="
echo "Backend URL: $BACKEND_URL"
echo ""

# Check if image file is provided
if [ -z "$IMAGE_FILE" ]; then
    echo "❌ No image file provided"
    echo "Usage: $0 [backend-url] [image-file]"
    echo "Example: $0 http://localhost:8080 test-image.jpg"
    exit 1
fi

# Check if image file exists
if [ ! -f "$IMAGE_FILE" ]; then
    echo "❌ Image file not found: $IMAGE_FILE"
    exit 1
fi

echo "📁 Image file: $IMAGE_FILE"
echo ""

# Test 1: Check upload service health
echo "🔍 Test 1: Checking upload service health..."
HEALTH_RESPONSE=$(curl -s -w "%{http_code}" -o /tmp/health_response.json "$BACKEND_URL/api/v1/upload/health")
HEALTH_CODE="${HEALTH_RESPONSE: -3}"

if [ "$HEALTH_CODE" = "200" ]; then
    echo "✅ Upload service is healthy"
    cat /tmp/health_response.json | jq . 2>/dev/null || cat /tmp/health_response.json
else
    echo "❌ Upload service health check failed (HTTP $HEALTH_CODE)"
    cat /tmp/health_response.json 2>/dev/null || echo "No response body"
    
    # Try the status endpoint if health fails
    echo ""
    echo "🔍 Checking upload status endpoint..."
    STATUS_RESPONSE=$(curl -s -w "%{http_code}" -o /tmp/status_response.json "$BACKEND_URL/api/v1/upload/status")
    STATUS_CODE="${STATUS_RESPONSE: -3}"
    
    if [ "$STATUS_CODE" = "503" ]; then
        echo "⚠️  Upload service is disabled (HTTP $STATUS_CODE)"
        cat /tmp/status_response.json | jq . 2>/dev/null || cat /tmp/status_response.json
    fi
    
    echo ""
    echo "💡 Upload service is not available. Check MinIO configuration."
    exit 1
fi

echo ""

# Test 2: Upload image
echo "🔍 Test 2: Uploading image..."
UPLOAD_RESPONSE=$(curl -s -w "%{http_code}" -o /tmp/upload_response.json \
    -X POST \
    -F "file=@$IMAGE_FILE" \
    "$BACKEND_URL/api/v1/upload/image")

UPLOAD_CODE="${UPLOAD_RESPONSE: -3}"

if [ "$UPLOAD_CODE" = "200" ]; then
    echo "✅ Image uploaded successfully"
    cat /tmp/upload_response.json | jq . 2>/dev/null || cat /tmp/upload_response.json
    
    # Extract image URL if available
    IMAGE_URL=$(cat /tmp/upload_response.json | jq -r '.url // .image.original.url // empty' 2>/dev/null)
    if [ ! -z "$IMAGE_URL" ]; then
        echo ""
        echo "🖼️  Image URL: $IMAGE_URL"
        
        # Test 3: Verify image is accessible
        echo ""
        echo "🔍 Test 3: Verifying image accessibility..."
        IMAGE_RESPONSE=$(curl -s -w "%{http_code}" -o /dev/null "$IMAGE_URL")
        IMAGE_CODE="${IMAGE_RESPONSE: -3}"
        
        if [ "$IMAGE_CODE" = "200" ]; then
            echo "✅ Image is accessible at the provided URL"
        else
            echo "⚠️  Image URL returned HTTP $IMAGE_CODE"
            echo "   This might be normal if the image requires authentication"
        fi
    fi
    
else
    echo "❌ Image upload failed (HTTP $UPLOAD_CODE)"
    cat /tmp/upload_response.json | jq . 2>/dev/null || cat /tmp/upload_response.json
    exit 1
fi

echo ""
echo "🎉 All tests completed successfully!"

# Cleanup
rm -f /tmp/health_response.json /tmp/status_response.json /tmp/upload_response.json
