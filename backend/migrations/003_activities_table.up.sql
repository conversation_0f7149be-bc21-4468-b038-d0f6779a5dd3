-- Migration: 003_activities_table
-- Description: Create activities table for tracking business operations
-- Created: 2025-07-01

-- Create activity type enum
CREATE TYPE activity_type AS ENUM (
    'order_created',
    'order_updated',
    'order_cancelled',
    'product_created',
    'product_updated',
    'product_deleted',
    'collection_created',
    'collection_updated',
    'collection_deleted',
    'customer_created',
    'customer_updated',
    'inventory_updated',
    'low_stock_alert',
    'out_of_stock_alert',
    'user_login',
    'user_logout'
);

-- Create activities table
CREATE TABLE activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    type activity_type NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    entity_type VARCHAR(50) NOT NULL, -- 'product', 'order', 'collection', 'customer', 'inventory', 'user'
    entity_id UUID,
    entity_name VARCHAR(255),
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    user_name VARCHAR(255),
    metadata JSONB, -- Additional activity-specific data
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for efficient querying
CREATE INDEX idx_activities_type ON activities(type);
CREATE INDEX idx_activities_entity_type ON activities(entity_type);
CREATE INDEX idx_activities_entity_id ON activities(entity_id);
CREATE INDEX idx_activities_user_id ON activities(user_id);
CREATE INDEX idx_activities_created_at ON activities(created_at DESC);
CREATE INDEX idx_activities_type_created_at ON activities(type, created_at DESC);
CREATE INDEX idx_activities_entity_type_created_at ON activities(entity_type, created_at DESC);

-- Create composite index for common queries
CREATE INDEX idx_activities_recent ON activities(created_at DESC, type, entity_type);
