-- Remove unwanted fields from products table
ALTER TABLE products
DROP COLUMN IF EXISTS price,
DROP COLUMN IF EXISTS cost_price,
DROP COLUMN IF EXISTS gemstone,
DROP COLUMN IF EXISTS dimensions;

-- Add new net weight field (keep existing weight field as gross weight)
ALTER TABLE products
ADD COLUMN net_weight DECIMAL(8, 3) DEFAULT 0.000;

-- Add comments to document the columns
COMMENT ON COLUMN products.weight IS 'Gross weight of the product in grams';
COMMENT ON COLUMN products.net_weight IS 'Net weight of the product in grams';
