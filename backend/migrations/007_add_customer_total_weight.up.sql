-- Add total_weight_grams field to customers table
-- This will track the total weight of all products ordered by the customer

ALTER TABLE customers 
ADD COLUMN total_weight_grams DECIMAL(10, 3) DEFAULT 0.000;

-- Create index for performance
CREATE INDEX IF NOT EXISTS idx_customers_total_weight_grams ON customers(total_weight_grams);

-- Update existing customers with calculated total weight from their orders
-- This calculates the total weight based on order_items.product_weight * quantity
UPDATE customers 
SET total_weight_grams = COALESCE((
    SELECT SUM(oi.product_weight * oi.quantity)
    FROM orders o
    JOIN order_items oi ON o.id = oi.order_id
    WHERE o.customer_id = customers.id
    AND o.status NOT IN ('cancelled')
    AND oi.product_weight IS NOT NULL
), 0.000);

-- Add comment to document the field
COMMENT ON COLUMN customers.total_weight_grams IS 'Total weight in grams of all products ordered by this customer (excluding cancelled orders)';
