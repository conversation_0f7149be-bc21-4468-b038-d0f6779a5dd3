-- Migration: 002_indexes_and_defaults (DOWN)
-- Description: Drop indexes and remove default settings
-- Created: 2025-06-29

-- Remove default settings
DELETE FROM settings WHERE key IN (
    'site_name',
    'contact_phone',
    'contact_email',
    'order_prefix',
    'collection_expiry_days',
    'max_items_per_collection',
    'currency',
    'tax_rate',
    'min_order_amount',
    'max_order_amount',
    'business_hours',
    'social_media',
    'shipping_info',
    'return_policy'
);

-- Drop indexes (PostgreSQL will automatically drop them when tables are dropped,
-- but listing them here for completeness)
-- Note: In practice, you rarely need to explicitly drop indexes in down migrations
-- unless you're doing partial rollbacks