package models

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

// OrderStatus represents the status of an order
type OrderStatus string

const (
	OrderStatusPending    OrderStatus = "pending"
	OrderStatusConfirmed  OrderStatus = "confirmed"
	OrderStatusProcessing OrderStatus = "processing"
	OrderStatusReady      OrderStatus = "ready"
	OrderStatusDelivered  OrderStatus = "delivered"
	OrderStatusCancelled  OrderStatus = "cancelled"
)

// Order represents an order in the system
type Order struct {
	ID              uuid.UUID       `json:"id" db:"id"`
	OrderNumber     string          `json:"order_number" db:"order_number"`
	CustomerID      uuid.UUID       `json:"customer_id" db:"customer_id"`
	Status          OrderStatus     `json:"status" db:"status"`
	TotalAmount     float64         `json:"total_amount" db:"total_amount"`
	DiscountAmount  float64         `json:"discount_amount" db:"discount_amount"`
	FinalAmount     float64         `json:"final_amount" db:"final_amount"`
	DeliveryAddress json.RawMessage `json:"delivery_address" db:"delivery_address"`
	DeliveryDate    *time.Time      `json:"delivery_date" db:"delivery_date"`
	Notes           *string         `json:"notes" db:"notes"`
	InternalNotes   *string         `json:"internal_notes" db:"internal_notes"`
	CreatedAt       time.Time       `json:"created_at" db:"created_at"`
	UpdatedAt       time.Time       `json:"updated_at" db:"updated_at"`

	// Related data (not stored in orders table)
	Customer      *Customer   `json:"customer,omitempty"`
	Items         []OrderItem `json:"items,omitempty"`
	ParsedAddress *Address    `json:"parsed_address,omitempty"`
}

// OrderItem represents an item in an order
type OrderItem struct {
	ID            uuid.UUID `json:"id" db:"id"`
	OrderID       uuid.UUID `json:"order_id" db:"order_id"`
	ProductID     uuid.UUID `json:"product_id" db:"product_id"`
	Quantity      int       `json:"quantity" db:"quantity"`
	UnitPrice     float64   `json:"unit_price" db:"unit_price"`
	TotalPrice    float64   `json:"total_price" db:"total_price"`
	ProductName   string    `json:"product_name" db:"product_name"`
	ProductSKU    string    `json:"product_sku" db:"product_sku"`
	ProductWeight float64   `json:"product_weight" db:"product_weight"`
	CreatedAt     time.Time `json:"created_at" db:"created_at"`

	// Related data (not stored in order_items table)
	Product *Product `json:"product,omitempty"`
}

// CreateOrderRequest represents request to create a new order
type CreateOrderRequest struct {
	CustomerID      uuid.UUID         `json:"customer_id" validate:"required"`
	Items           []CreateOrderItem `json:"items" validate:"required,min=1"`
	DeliveryAddress *Address          `json:"delivery_address"`
	DeliveryDate    *time.Time        `json:"delivery_date"`
	DiscountAmount  float64           `json:"discount_amount" validate:"min=0"`
	Notes           *string           `json:"notes"`
	InternalNotes   *string           `json:"internal_notes"`
}

// CreateOrderItem represents an item in a create order request
type CreateOrderItem struct {
	ProductID uuid.UUID `json:"product_id" validate:"required"`
	Quantity  int       `json:"quantity" validate:"required,min=1"`
}

// UpdateOrderRequest represents request to update an order
type UpdateOrderRequest struct {
	Status          *OrderStatus `json:"status" validate:"omitempty,oneof=pending confirmed processing ready delivered cancelled"`
	DeliveryAddress *Address     `json:"delivery_address"`
	DeliveryDate    *time.Time   `json:"delivery_date"`
	DiscountAmount  *float64     `json:"discount_amount" validate:"omitempty,min=0"`
	Notes           *string      `json:"notes"`
	InternalNotes   *string      `json:"internal_notes"`
}

// OrderListRequest represents request parameters for listing orders
type OrderListRequest struct {
	Page       int          `json:"page" validate:"min=1"`
	Limit      int          `json:"limit" validate:"min=1,max=100"`
	CustomerID *uuid.UUID   `json:"customer_id"`
	Status     *OrderStatus `json:"status"`
	Search     *string      `json:"search"`
	DateFrom   *time.Time   `json:"date_from"`
	DateTo     *time.Time   `json:"date_to"`
	SortBy     string       `json:"sort_by"`    // "order_number", "total_amount", "created_at", "updated_at"
	SortOrder  string       `json:"sort_order"` // "asc", "desc"
}

// OrderResponse represents order data for API responses
type OrderResponse struct {
	ID              uuid.UUID           `json:"id"`
	OrderNumber     string              `json:"order_number"`
	CustomerID      uuid.UUID           `json:"customer_id"`
	Status          OrderStatus         `json:"status"`
	TotalAmount     float64             `json:"total_amount"`
	DiscountAmount  float64             `json:"discount_amount"`
	FinalAmount     float64             `json:"final_amount"`
	DeliveryAddress *Address            `json:"delivery_address"`
	DeliveryDate    *time.Time          `json:"delivery_date"`
	Notes           *string             `json:"notes"`
	InternalNotes   *string             `json:"internal_notes"`
	CreatedAt       time.Time           `json:"created_at"`
	UpdatedAt       time.Time           `json:"updated_at"`
	Customer        *CustomerResponse   `json:"customer,omitempty"`
	Items           []OrderItemResponse `json:"items,omitempty"`
}

// OrderItemResponse represents order item data for API responses
type OrderItemResponse struct {
	ID            uuid.UUID        `json:"id"`
	ProductID     uuid.UUID        `json:"product_id"`
	Quantity      int              `json:"quantity"`
	UnitPrice     float64          `json:"unit_price"`
	TotalPrice    float64          `json:"total_price"`
	ProductName   string           `json:"product_name"`
	ProductSKU    string           `json:"product_sku"`
	ProductWeight float64          `json:"product_weight"`
	Product       *ProductResponse `json:"product,omitempty"`
}

// ToResponse converts Order to OrderResponse
func (o *Order) ToResponse() *OrderResponse {
	response := &OrderResponse{
		ID:             o.ID,
		OrderNumber:    o.OrderNumber,
		CustomerID:     o.CustomerID,
		Status:         o.Status,
		TotalAmount:    o.TotalAmount,
		DiscountAmount: o.DiscountAmount,
		FinalAmount:    o.FinalAmount,
		DeliveryDate:   o.DeliveryDate,
		Notes:          o.Notes,
		InternalNotes:  o.InternalNotes,
		CreatedAt:      o.CreatedAt,
		UpdatedAt:      o.UpdatedAt,
	}

	// Parse delivery address if available
	if len(o.DeliveryAddress) > 0 {
		var addr Address
		if err := json.Unmarshal(o.DeliveryAddress, &addr); err == nil {
			response.DeliveryAddress = &addr
		}
	}

	// Convert customer if available
	if o.Customer != nil {
		response.Customer = o.Customer.ToResponse()
	}

	// Convert items if available
	if len(o.Items) > 0 {
		response.Items = make([]OrderItemResponse, len(o.Items))
		for i, item := range o.Items {
			response.Items[i] = OrderItemResponse{
				ID:            item.ID,
				ProductID:     item.ProductID,
				Quantity:      item.Quantity,
				UnitPrice:     item.UnitPrice,
				TotalPrice:    item.TotalPrice,
				ProductName:   item.ProductName,
				ProductSKU:    item.ProductSKU,
				ProductWeight: item.ProductWeight,
			}
			if item.Product != nil {
				response.Items[i].Product = item.Product.ToResponse()
			}
		}
	}

	return response
}

// GenerateOrderNumber generates a unique order number
func GenerateOrderNumber() string {
	return "ORD-" + time.Now().Format("20060102") + "-" + uuid.New().String()[:8]
}
