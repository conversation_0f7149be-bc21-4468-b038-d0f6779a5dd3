package models

import (
	"encoding/json"
	"time"

	"github.com/google/uuid"
)

// Address represents a customer address
type Address struct {
	Street   string `json:"street"`
	City     string `json:"city"`
	State    string `json:"state"`
	Country  string `json:"country"`
	Pincode  string `json:"pincode"`
	Landmark string `json:"landmark,omitempty"`
}

// Customer represents a customer in the system
type Customer struct {
	ID              uuid.UUID       `json:"id" db:"id"`
	Name            string          `json:"name" db:"name"`
	Email           string          `json:"email" db:"email"`
	Phone           string          `json:"phone" db:"phone"`
	AlternatePhone  *string         `json:"alternate_phone" db:"alternate_phone"`
	Address         json.RawMessage `json:"address" db:"address"`
	TotalOrders     int             `json:"total_orders" db:"total_orders"`
	TotalSpent      float64         `json:"total_spent" db:"total_spent"`
	LastOrderDate   *time.Time      `json:"last_order_date" db:"last_order_date"`
	PreferredMethod string          `json:"preferred_method" db:"preferred_method"`
	Notes           *string         `json:"notes" db:"notes"`
	IsActive        bool            `json:"is_active" db:"is_active"`
	CreatedAt       time.Time       `json:"created_at" db:"created_at"`
	UpdatedAt       time.Time       `json:"updated_at" db:"updated_at"`

	// Parsed address for convenience
	ParsedAddress *Address `json:"parsed_address,omitempty"`
}

// CreateCustomerRequest represents request to create a new customer
type CreateCustomerRequest struct {
	Name            string   `json:"name" validate:"required,min=1,max=255"`
	Email           string   `json:"email" validate:"required,email"`
	Phone           string   `json:"phone" validate:"required,min=10,max=15"`
	AlternatePhone  *string  `json:"alternate_phone" validate:"omitempty,min=10,max=15"`
	Address         *Address `json:"address"`
	PreferredMethod string   `json:"preferred_method" validate:"omitempty,oneof=phone email whatsapp"`
	Notes           *string  `json:"notes"`
}

// UpdateCustomerRequest represents request to update a customer
type UpdateCustomerRequest struct {
	Name            *string  `json:"name" validate:"omitempty,min=1,max=255"`
	Email           *string  `json:"email" validate:"omitempty,email"`
	Phone           *string  `json:"phone" validate:"omitempty,min=10,max=15"`
	AlternatePhone  *string  `json:"alternate_phone" validate:"omitempty,min=10,max=15"`
	Address         *Address `json:"address"`
	PreferredMethod *string  `json:"preferred_method" validate:"omitempty,oneof=phone email whatsapp"`
	Notes           *string  `json:"notes"`
	IsActive        *bool    `json:"is_active"`
}

// CustomerListRequest represents request parameters for listing customers
type CustomerListRequest struct {
	Page      int     `json:"page" validate:"min=1"`
	Limit     int     `json:"limit" validate:"min=1,max=100"`
	Search    *string `json:"search"`
	IsActive  *bool   `json:"is_active"`
	SortBy    string  `json:"sort_by"`    // "name", "email", "total_orders", "total_spent", "created_at"
	SortOrder string  `json:"sort_order"` // "asc", "desc"
}

// CustomerResponse represents customer data for API responses
type CustomerResponse struct {
	ID              uuid.UUID  `json:"id"`
	Name            string     `json:"name"`
	Email           string     `json:"email"`
	Phone           string     `json:"phone"`
	AlternatePhone  *string    `json:"alternate_phone"`
	Address         *Address   `json:"address"`
	TotalOrders     int        `json:"total_orders"`
	TotalSpent      float64    `json:"total_spent"`
	LastOrderDate   *time.Time `json:"last_order_date"`
	PreferredMethod string     `json:"preferred_method"`
	Notes           *string    `json:"notes"`
	IsActive        bool       `json:"is_active"`
	CreatedAt       time.Time  `json:"created_at"`
	UpdatedAt       time.Time  `json:"updated_at"`
}

// ToResponse converts Customer to CustomerResponse
func (c *Customer) ToResponse() *CustomerResponse {
	response := &CustomerResponse{
		ID:              c.ID,
		Name:            c.Name,
		Email:           c.Email,
		Phone:           c.Phone,
		AlternatePhone:  c.AlternatePhone,
		TotalOrders:     c.TotalOrders,
		TotalSpent:      c.TotalSpent,
		LastOrderDate:   c.LastOrderDate,
		PreferredMethod: c.PreferredMethod,
		Notes:           c.Notes,
		IsActive:        c.IsActive,
		CreatedAt:       c.CreatedAt,
		UpdatedAt:       c.UpdatedAt,
	}

	// Parse address if available
	if len(c.Address) > 0 {
		var addr Address
		if err := json.Unmarshal(c.Address, &addr); err == nil {
			response.Address = &addr
		}
	}

	return response
}

// ParseAddress parses the JSON address field
func (c *Customer) ParseAddress() *Address {
	if len(c.Address) == 0 {
		return nil
	}

	var addr Address
	if err := json.Unmarshal(c.Address, &addr); err != nil {
		return nil
	}

	return &addr
}
