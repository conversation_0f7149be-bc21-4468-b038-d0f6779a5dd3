package models

import (
	"time"

	"github.com/google/uuid"
)

// InventoryChangeType represents the type of inventory change
type InventoryChangeType string

const (
	InventoryChangeIncrease InventoryChangeType = "increase"
	InventoryChangeDecrease InventoryChangeType = "decrease"
	InventoryChangeAdjust   InventoryChangeType = "adjust"
)

// InventoryLog represents an inventory change log entry
type InventoryLog struct {
	ID             uuid.UUID           `json:"id" db:"id"`
	ProductID      uuid.UUID           `json:"product_id" db:"product_id"`
	ChangeType     InventoryChangeType `json:"change_type" db:"change_type"`
	QuantityChange int                 `json:"quantity_change" db:"quantity_change"`
	PreviousStock  int                 `json:"previous_stock" db:"previous_stock"`
	NewStock       int                 `json:"new_stock" db:"new_stock"`
	Reason         string              `json:"reason" db:"reason"`
	CreatedBy      *uuid.UUID          `json:"created_by" db:"created_by"`
	CreatedAt      time.Time           `json:"created_at" db:"created_at"`
	
	// Related data
	Product *Product `json:"product,omitempty"`
}

// InventoryStatus represents the current inventory status of a product
type InventoryStatus struct {
	ProductID      uuid.UUID `json:"product_id"`
	ProductName    string    `json:"product_name"`
	ProductSKU     string    `json:"product_sku"`
	CurrentStock   int       `json:"current_stock"`
	MinStockLevel  int       `json:"min_stock_level"`
	IsLowStock     bool      `json:"is_low_stock"`
	IsOutOfStock   bool      `json:"is_out_of_stock"`
	LastUpdated    time.Time `json:"last_updated"`
	
	// Related data
	Product *Product `json:"product,omitempty"`
}

// UpdateInventoryRequest represents request to update product inventory
type UpdateInventoryRequest struct {
	Quantity   int    `json:"quantity" validate:"required,min=0"`
	ChangeType string `json:"change_type" validate:"required,oneof=increase decrease adjust"`
	Reason     string `json:"reason" validate:"required,min=1,max=255"`
}

// BulkInventoryUpdateRequest represents request for bulk inventory update
type BulkInventoryUpdateRequest struct {
	Updates []BulkInventoryItem `json:"updates" validate:"required,min=1"`
	Reason  string              `json:"reason" validate:"required,min=1,max=255"`
}

// BulkInventoryItem represents a single item in bulk inventory update
type BulkInventoryItem struct {
	ProductID  uuid.UUID `json:"product_id" validate:"required"`
	Quantity   int       `json:"quantity" validate:"required,min=0"`
	ChangeType string    `json:"change_type" validate:"required,oneof=increase decrease adjust"`
}

// InventoryListRequest represents request parameters for listing inventory
type InventoryListRequest struct {
	Page         int     `json:"page" validate:"min=1"`
	Limit        int     `json:"limit" validate:"min=1,max=100"`
	Category     *string `json:"category"`
	LowStockOnly bool    `json:"low_stock_only"`
	OutOfStock   bool    `json:"out_of_stock"`
	Search       *string `json:"search"`
	SortBy       string  `json:"sort_by"`    // "name", "sku", "stock", "last_updated"
	SortOrder    string  `json:"sort_order"` // "asc", "desc"
}

// InventoryLogListRequest represents request parameters for listing inventory logs
type InventoryLogListRequest struct {
	Page      int        `json:"page" validate:"min=1"`
	Limit     int        `json:"limit" validate:"min=1,max=100"`
	ProductID *uuid.UUID `json:"product_id"`
	DateFrom  *time.Time `json:"date_from"`
	DateTo    *time.Time `json:"date_to"`
	SortBy    string     `json:"sort_by"`    // "created_at", "quantity_change"
	SortOrder string     `json:"sort_order"` // "asc", "desc"
}

// InventoryResponse represents inventory data for API responses
type InventoryResponse struct {
	ProductID      uuid.UUID        `json:"product_id"`
	ProductName    string           `json:"product_name"`
	ProductSKU     string           `json:"product_sku"`
	CurrentStock   int              `json:"current_stock"`
	MinStockLevel  int              `json:"min_stock_level"`
	IsLowStock     bool             `json:"is_low_stock"`
	IsOutOfStock   bool             `json:"is_out_of_stock"`
	LastUpdated    time.Time        `json:"last_updated"`
	Product        *ProductResponse `json:"product,omitempty"`
}

// InventoryLogResponse represents inventory log data for API responses
type InventoryLogResponse struct {
	ID             uuid.UUID           `json:"id"`
	ProductID      uuid.UUID           `json:"product_id"`
	ChangeType     InventoryChangeType `json:"change_type"`
	QuantityChange int                 `json:"quantity_change"`
	PreviousStock  int                 `json:"previous_stock"`
	NewStock       int                 `json:"new_stock"`
	Reason         string              `json:"reason"`
	CreatedAt      time.Time           `json:"created_at"`
	Product        *ProductResponse    `json:"product,omitempty"`
}

// ToResponse converts InventoryStatus to InventoryResponse
func (i *InventoryStatus) ToResponse() *InventoryResponse {
	response := &InventoryResponse{
		ProductID:     i.ProductID,
		ProductName:   i.ProductName,
		ProductSKU:    i.ProductSKU,
		CurrentStock:  i.CurrentStock,
		MinStockLevel: i.MinStockLevel,
		IsLowStock:    i.IsLowStock,
		IsOutOfStock:  i.IsOutOfStock,
		LastUpdated:   i.LastUpdated,
	}

	if i.Product != nil {
		response.Product = i.Product.ToResponse()
	}

	return response
}

// ToResponse converts InventoryLog to InventoryLogResponse
func (i *InventoryLog) ToResponse() *InventoryLogResponse {
	response := &InventoryLogResponse{
		ID:             i.ID,
		ProductID:      i.ProductID,
		ChangeType:     i.ChangeType,
		QuantityChange: i.QuantityChange,
		PreviousStock:  i.PreviousStock,
		NewStock:       i.NewStock,
		Reason:         i.Reason,
		CreatedAt:      i.CreatedAt,
	}

	if i.Product != nil {
		response.Product = i.Product.ToResponse()
	}

	return response
}
