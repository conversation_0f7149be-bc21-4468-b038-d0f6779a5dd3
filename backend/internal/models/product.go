package models

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/lib/pq"
)

// AvailabilityStatus represents product availability enum
type AvailabilityStatus string

const (
	AvailabilityAvailable    AvailabilityStatus = "available"
	AvailabilityOutOfStock   AvailabilityStatus = "out_of_stock"
	AvailabilityDiscontinued AvailabilityStatus = "discontinued"
)

// Dimensions represents product dimensions
type Dimensions struct {
	Length float64 `json:"length"`
	Width  float64 `json:"width"`
	Height float64 `json:"height"`
	Unit   string  `json:"unit"` // "mm", "cm", "inch"
}

// Value implements driver.Valuer interface for database storage
func (d Dimensions) Value() (driver.Value, error) {
	return json.Marshal(d)
}

// Scan implements sql.Scanner interface for database retrieval
func (d *Dimensions) Scan(value interface{}) error {
	if value == nil {
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return fmt.Errorf("cannot scan %T into Dimensions", value)
	}

	return json.Unmarshal(bytes, d)
}

// Product represents a jewelry product
type Product struct {
	ID            uuid.UUID          `json:"id" db:"id"`
	SKU           string             `json:"sku" db:"sku"`
	Name          string             `json:"name" db:"name"`
	Description   *string            `json:"description" db:"description"`
	Category      string             `json:"category" db:"category"`
	Subcategory   *string            `json:"subcategory" db:"subcategory"`
	Weight        float64            `json:"weight" db:"weight"`         // Gross weight in grams (displayed as "gross weight" on frontend)
	NetWeight     float64            `json:"net_weight" db:"net_weight"` // Net weight in grams
	Material      *string            `json:"material" db:"material"`
	MetalPurity   *string            `json:"metal_purity" db:"metal_purity"`
	Availability  AvailabilityStatus `json:"availability" db:"availability"`
	StockQuantity int                `json:"stock_quantity" db:"stock_quantity"`
	MinStockLevel int                `json:"min_stock_level" db:"min_stock_level"`
	Tags          pq.StringArray     `json:"tags" db:"tags"`
	IsFeatured    bool               `json:"is_featured" db:"is_featured"`
	IsActive      bool               `json:"is_active" db:"is_active"`
	CreatedBy     *uuid.UUID         `json:"created_by" db:"created_by"`
	CreatedAt     time.Time          `json:"created_at" db:"created_at"`
	UpdatedAt     time.Time          `json:"updated_at" db:"updated_at"`

	// Related data (not stored in products table)
	Images       []ProductImage `json:"images,omitempty"`
	PrimaryImage *ProductImage  `json:"primary_image,omitempty"`
}

// ProductImage represents a product image
type ProductImage struct {
	ID           uuid.UUID `json:"id" db:"id"`
	ProductID    uuid.UUID `json:"product_id" db:"product_id"`
	ImageURL     string    `json:"image_url" db:"image_url"`
	ThumbnailURL *string   `json:"thumbnail_url" db:"thumbnail_url"`
	MediumURL    *string   `json:"medium_url" db:"medium_url"`
	LargeURL     *string   `json:"large_url" db:"large_url"`
	AltText      *string   `json:"alt_text" db:"alt_text"`
	DisplayOrder int       `json:"display_order" db:"display_order"`
	IsPrimary    bool      `json:"is_primary" db:"is_primary"`
	FileSize     *int      `json:"file_size" db:"file_size"`
	Width        *int      `json:"width" db:"width"`
	Height       *int      `json:"height" db:"height"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
}

// CreateProductImageRequest represents an image to be associated with a product during creation
type CreateProductImageRequest struct {
	URL       string `json:"url" validate:"required"`
	AltText   string `json:"alt"`
	IsPrimary bool   `json:"is_primary"`
	SortOrder int    `json:"sort_order"`
}

// CreateProductRequest represents request to create a new product
type CreateProductRequest struct {
	SKU           string                      `json:"sku" validate:"required"`
	Name          string                      `json:"name" validate:"required"`
	Description   *string                     `json:"description"`
	Category      string                      `json:"category" validate:"required"`
	Subcategory   *string                     `json:"subcategory"`
	Weight        float64                     `json:"weight" validate:"min=0"`     // Gross weight (displayed as "gross weight" on frontend)
	NetWeight     float64                     `json:"net_weight" validate:"min=0"` // Net weight
	Material      *string                     `json:"material"`
	MetalPurity   *string                     `json:"metal_purity"`
	StockQuantity int                         `json:"stock_quantity" validate:"min=0"`
	MinStockLevel int                         `json:"min_stock_level" validate:"min=0"`
	Tags          []string                    `json:"tags"`
	IsFeatured    bool                        `json:"is_featured"`
	Images        []CreateProductImageRequest `json:"images,omitempty"`
}

// UpdateProductRequest represents request to update a product
type UpdateProductRequest struct {
	Name          *string             `json:"name"`
	Description   *string             `json:"description"`
	Category      *string             `json:"category"`
	Subcategory   *string             `json:"subcategory"`
	Weight        *float64            `json:"weight" validate:"omitempty,min=0"`     // Gross weight (displayed as "gross weight" on frontend)
	NetWeight     *float64            `json:"net_weight" validate:"omitempty,min=0"` // Net weight
	Material      *string             `json:"material"`
	MetalPurity   *string             `json:"metal_purity"`
	Availability  *AvailabilityStatus `json:"availability"`
	StockQuantity *int                `json:"stock_quantity" validate:"omitempty,min=0"`
	MinStockLevel *int                `json:"min_stock_level" validate:"omitempty,min=0"`
	Tags          []string            `json:"tags"`
	IsFeatured    *bool               `json:"is_featured"`
	IsActive      *bool               `json:"is_active"`
}

// ProductListRequest represents request parameters for listing products
type ProductListRequest struct {
	Page         int                 `json:"page" validate:"min=1"`
	Limit        int                 `json:"limit" validate:"min=1,max=100"`
	Category     *string             `json:"category"`
	Subcategory  *string             `json:"subcategory"`
	Availability *AvailabilityStatus `json:"availability"`
	IsFeatured   *bool               `json:"is_featured"`
	IsActive     *bool               `json:"is_active"`
	Search       *string             `json:"search"`
	Tags         []string            `json:"tags"`
	MinPrice     *float64            `json:"min_price" validate:"omitempty,min=0"`
	MaxPrice     *float64            `json:"max_price" validate:"omitempty,min=0"`
	SortBy       string              `json:"sort_by"`    // "name", "price", "created_at", "updated_at"
	SortOrder    string              `json:"sort_order"` // "asc", "desc"
}

// ProductResponse represents product data for API responses
type ProductResponse struct {
	ID            uuid.UUID          `json:"id"`
	SKU           string             `json:"sku"`
	Name          string             `json:"name"`
	Description   *string            `json:"description"`
	Category      string             `json:"category"`
	Subcategory   *string            `json:"subcategory"`
	Weight        float64            `json:"weight"`     // Gross weight (displayed as "gross weight" on frontend)
	NetWeight     float64            `json:"net_weight"` // Net weight
	Material      *string            `json:"material"`
	MetalPurity   *string            `json:"metal_purity"`
	Availability  AvailabilityStatus `json:"availability"`
	StockQuantity int                `json:"stock_quantity"`
	MinStockLevel int                `json:"min_stock_level"`
	Tags          []string           `json:"tags"`
	IsFeatured    bool               `json:"is_featured"`
	IsActive      bool               `json:"is_active"`
	CreatedAt     time.Time          `json:"created_at"`
	UpdatedAt     time.Time          `json:"updated_at"`
	Images        []ProductImage     `json:"images"`
	PrimaryImage  *ProductImage      `json:"primary_image"`
}

// ToResponse converts Product to ProductResponse
func (p *Product) ToResponse() *ProductResponse {
	tags := make([]string, len(p.Tags))
	copy(tags, p.Tags)

	return &ProductResponse{
		ID:            p.ID,
		SKU:           p.SKU,
		Name:          p.Name,
		Description:   p.Description,
		Category:      p.Category,
		Subcategory:   p.Subcategory,
		Weight:        p.Weight,
		NetWeight:     p.NetWeight,
		Material:      p.Material,
		MetalPurity:   p.MetalPurity,
		Availability:  p.Availability,
		StockQuantity: p.StockQuantity,
		MinStockLevel: p.MinStockLevel,
		Tags:          tags,
		IsFeatured:    p.IsFeatured,
		IsActive:      p.IsActive,
		CreatedAt:     p.CreatedAt,
		UpdatedAt:     p.UpdatedAt,
		Images:        p.Images,
		PrimaryImage:  p.PrimaryImage,
	}
}
