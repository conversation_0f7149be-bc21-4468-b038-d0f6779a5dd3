package models

import (
	"time"

	"github.com/google/uuid"
)

// UserRole represents user role enum
type UserRole string

const (
	RoleAdmin      UserRole = "admin"
	RoleSuperAdmin UserRole = "super_admin"
)

// User represents a user in the system (Google OAuth)
type User struct {
	ID         uuid.UUID  `json:"id" db:"id"`
	GoogleID   string     `json:"google_id" db:"google_id"`
	Email      string     `json:"email" db:"email"`
	Name       string     `json:"name" db:"name"`
	PictureURL *string    `json:"picture_url" db:"picture_url"`
	Role       UserRole   `json:"role" db:"role"`
	IsActive   bool       `json:"is_active" db:"is_active"`
	LastLogin  *time.Time `json:"last_login" db:"last_login"`
	CreatedAt  time.Time  `json:"created_at" db:"created_at"`
	UpdatedAt  time.Time  `json:"updated_at" db:"updated_at"`
}

// CreateUserRequest represents request to create a new user
type CreateUserRequest struct {
	GoogleID   string   `json:"google_id" validate:"required"`
	Email      string   `json:"email" validate:"required,email"`
	Name       string   `json:"name" validate:"required"`
	PictureURL *string  `json:"picture_url"`
	Role       UserRole `json:"role"`
}

// UpdateUserRequest represents request to update user
type UpdateUserRequest struct {
	Name       *string   `json:"name"`
	PictureURL *string   `json:"picture_url"`
	Role       *UserRole `json:"role"`
	IsActive   *bool     `json:"is_active"`
}

// UserResponse represents user data for API responses (without sensitive info)
type UserResponse struct {
	ID         uuid.UUID  `json:"id"`
	Email      string     `json:"email"`
	Name       string     `json:"name"`
	PictureURL *string    `json:"picture_url"`
	Role       UserRole   `json:"role"`
	IsActive   bool       `json:"is_active"`
	LastLogin  *time.Time `json:"last_login"`
	CreatedAt  time.Time  `json:"created_at"`
}

// ToResponse converts User to UserResponse
func (u *User) ToResponse() *UserResponse {
	return &UserResponse{
		ID:         u.ID,
		Email:      u.Email,
		Name:       u.Name,
		PictureURL: u.PictureURL,
		Role:       u.Role,
		IsActive:   u.IsActive,
		LastLogin:  u.LastLogin,
		CreatedAt:  u.CreatedAt,
	}
}
