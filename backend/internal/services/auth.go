package services

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/anandjewels/jewelry-backend/internal/database"
	"github.com/anandjewels/jewelry-backend/internal/models"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"golang.org/x/oauth2"
	"golang.org/x/oauth2/google"
)

// AuthService handles authentication operations
type AuthService struct {
	db           *database.DB
	googleConfig *oauth2.Config
	jwtSecret    []byte
	jwtExpiry    time.Duration
	adminEmails  map[string]bool
}

// GoogleUserInfo represents user info from Google OAuth
type GoogleUserInfo struct {
	ID            string `json:"id"`
	Email         string `json:"email"`
	VerifiedEmail bool   `json:"verified_email"`
	Name          string `json:"name"`
	GivenName     string `json:"given_name"`
	FamilyName    string `json:"family_name"`
	Picture       string `json:"picture"`
	Locale        string `json:"locale"`
}

// JWTClaims represents JWT token claims
type JWTClaims struct {
	UserID   uuid.UUID        `json:"user_id"`
	Email    string           `json:"email"`
	Name     string           `json:"name"`
	Role     models.UserRole  `json:"role"`
	IsActive bool             `json:"is_active"`
	jwt.RegisteredClaims
}

// NewAuthService creates a new authentication service
func NewAuthService(db *database.DB) (*AuthService, error) {
	// Get Google OAuth configuration
	clientID := os.Getenv("GOOGLE_CLIENT_ID")
	clientSecret := os.Getenv("GOOGLE_CLIENT_SECRET")
	redirectURL := os.Getenv("GOOGLE_REDIRECT_URL")

	if clientID == "" || clientSecret == "" || redirectURL == "" {
		return nil, fmt.Errorf("missing Google OAuth configuration")
	}

	googleConfig := &oauth2.Config{
		ClientID:     clientID,
		ClientSecret: clientSecret,
		RedirectURL:  redirectURL,
		Scopes: []string{
			"https://www.googleapis.com/auth/userinfo.email",
			"https://www.googleapis.com/auth/userinfo.profile",
		},
		Endpoint: google.Endpoint,
	}

	// Get JWT configuration
	jwtSecret := os.Getenv("JWT_SECRET")
	if jwtSecret == "" {
		return nil, fmt.Errorf("JWT_SECRET environment variable is required")
	}

	jwtExpiryHours := 24 // default
	if expiryStr := os.Getenv("JWT_EXPIRY_HOURS"); expiryStr != "" {
		if hours, err := time.ParseDuration(expiryStr + "h"); err == nil {
			jwtExpiryHours = int(hours.Hours())
		}
	}

	// Parse admin whitelist
	adminEmails := make(map[string]bool)
	if whitelist := os.Getenv("ADMIN_WHITELIST"); whitelist != "" {
		emails := strings.Split(whitelist, ",")
		for _, email := range emails {
			email = strings.TrimSpace(email)
			if email != "" {
				adminEmails[strings.ToLower(email)] = true
			}
		}
	}

	return &AuthService{
		db:           db,
		googleConfig: googleConfig,
		jwtSecret:    []byte(jwtSecret),
		jwtExpiry:    time.Duration(jwtExpiryHours) * time.Hour,
		adminEmails:  adminEmails,
	}, nil
}

// GenerateState generates a random state string for OAuth
func (s *AuthService) GenerateState() (string, error) {
	b := make([]byte, 32)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(b), nil
}

// GetAuthURL returns the Google OAuth authorization URL
func (s *AuthService) GetAuthURL(state string) string {
	return s.googleConfig.AuthCodeURL(state, oauth2.AccessTypeOffline)
}

// ExchangeCodeForToken exchanges authorization code for access token
func (s *AuthService) ExchangeCodeForToken(ctx context.Context, code string) (*oauth2.Token, error) {
	return s.googleConfig.Exchange(ctx, code)
}

// GetUserInfo retrieves user information from Google
func (s *AuthService) GetUserInfo(ctx context.Context, token *oauth2.Token) (*GoogleUserInfo, error) {
	client := s.googleConfig.Client(ctx, token)
	resp, err := client.Get("https://www.googleapis.com/oauth2/v2/userinfo")
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to get user info: %s", resp.Status)
	}

	var userInfo GoogleUserInfo
	if err := json.NewDecoder(resp.Body).Decode(&userInfo); err != nil {
		return nil, err
	}

	return &userInfo, nil
}

// IsAdminEmail checks if email is in the admin whitelist
func (s *AuthService) IsAdminEmail(email string) bool {
	return s.adminEmails[strings.ToLower(email)]
}

// CreateOrUpdateUser creates or updates user in database
func (s *AuthService) CreateOrUpdateUser(ctx context.Context, userInfo *GoogleUserInfo) (*models.User, error) {
	// Check if user exists
	var user models.User
	query := `
		SELECT id, google_id, email, name, picture_url, role, is_active, last_login, created_at, updated_at
		FROM users WHERE google_id = $1 OR email = $2
	`
	
	err := s.db.Postgres.QueryRowContext(ctx, query, userInfo.ID, userInfo.Email).Scan(
		&user.ID, &user.GoogleID, &user.Email, &user.Name, &user.PictureURL,
		&user.Role, &user.IsActive, &user.LastLogin, &user.CreatedAt, &user.UpdatedAt,
	)

	now := time.Now()
	
	if err != nil {
		// User doesn't exist, create new one
		user.ID = uuid.New()
		user.GoogleID = userInfo.ID
		user.Email = userInfo.Email
		user.Name = userInfo.Name
		if userInfo.Picture != "" {
			user.PictureURL = &userInfo.Picture
		}
		
		// Set role based on whitelist
		if s.IsAdminEmail(userInfo.Email) {
			user.Role = models.RoleAdmin
			user.IsActive = true
		} else {
			// Non-whitelisted users are not allowed
			return nil, fmt.Errorf("email %s is not authorized for admin access", userInfo.Email)
		}
		
		user.LastLogin = &now
		user.CreatedAt = now
		user.UpdatedAt = now

		// Insert new user
		insertQuery := `
			INSERT INTO users (id, google_id, email, name, picture_url, role, is_active, last_login, created_at, updated_at)
			VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
		`
		_, err = s.db.Postgres.ExecContext(ctx, insertQuery,
			user.ID, user.GoogleID, user.Email, user.Name, user.PictureURL,
			user.Role, user.IsActive, user.LastLogin, user.CreatedAt, user.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to create user: %w", err)
		}
	} else {
		// User exists, check if they're still whitelisted
		if !s.IsAdminEmail(user.Email) {
			return nil, fmt.Errorf("email %s is no longer authorized for admin access", user.Email)
		}

		// Update existing user
		user.Name = userInfo.Name
		if userInfo.Picture != "" {
			user.PictureURL = &userInfo.Picture
		}
		user.LastLogin = &now
		user.UpdatedAt = now

		updateQuery := `
			UPDATE users SET name = $2, picture_url = $3, last_login = $4, updated_at = $5
			WHERE id = $1
		`
		_, err = s.db.Postgres.ExecContext(ctx, updateQuery,
			user.ID, user.Name, user.PictureURL, user.LastLogin, user.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to update user: %w", err)
		}
	}

	return &user, nil
}

// GenerateJWT generates a JWT token for the user
func (s *AuthService) GenerateJWT(user *models.User) (string, error) {
	claims := JWTClaims{
		UserID:   user.ID,
		Email:    user.Email,
		Name:     user.Name,
		Role:     user.Role,
		IsActive: user.IsActive,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(s.jwtExpiry)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "jewelry-backend",
			Subject:   user.ID.String(),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString(s.jwtSecret)
}

// ValidateJWT validates and parses a JWT token
func (s *AuthService) ValidateJWT(tokenString string) (*JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return s.jwtSecret, nil
	})

	if err != nil {
		return nil, err
	}

	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}

	return nil, fmt.Errorf("invalid token")
}
