package services

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/anandjewels/jewelry-backend/internal/database"
	"github.com/anandjewels/jewelry-backend/internal/models"
	"github.com/google/uuid"
)

// ActivityService handles activity-related business logic
type ActivityService struct {
	db *database.DB
}

// NewActivityService creates a new activity service
func NewActivityService(db *database.DB) *ActivityService {
	return &ActivityService{db: db}
}

// CreateActivity creates a new activity record
func (s *ActivityService) CreateActivity(req *models.CreateActivityRequest) (*models.Activity, error) {
	activity := &models.Activity{
		ID:          uuid.New(),
		Type:        req.Type,
		Title:       req.Title,
		Description: req.Description,
		EntityType:  req.EntityType,
		EntityID:    req.EntityID,
		EntityName:  req.EntityName,
		UserID:      req.UserID,
		UserName:    req.UserName,
		CreatedAt:   time.Now(),
	}

	// Convert metadata to JSON
	if req.Metadata != nil {
		metadataJSON, err := json.Marshal(req.Metadata)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal metadata: %w", err)
		}
		activity.Metadata = metadataJSON
	}

	query := `
		INSERT INTO activities (
			id, type, title, description, entity_type, entity_id, entity_name,
			user_id, user_name, metadata, created_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11
		)`

	_, err := s.db.Postgres.Exec(query,
		activity.ID, activity.Type, activity.Title, activity.Description,
		activity.EntityType, activity.EntityID, activity.EntityName,
		activity.UserID, activity.UserName, activity.Metadata, activity.CreatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to create activity: %w", err)
	}

	return activity, nil
}

// GetActivities retrieves activities with filtering and pagination
func (s *ActivityService) GetActivities(req *models.ActivityListRequest) ([]*models.Activity, int, error) {
	// Build WHERE clause
	var conditions []string
	var args []interface{}
	argIndex := 1

	if req.Type != "" {
		conditions = append(conditions, fmt.Sprintf("type = $%d", argIndex))
		args = append(args, req.Type)
		argIndex++
	}

	if req.EntityType != "" {
		conditions = append(conditions, fmt.Sprintf("entity_type = $%d", argIndex))
		args = append(args, req.EntityType)
		argIndex++
	}

	if req.StartDate != "" {
		conditions = append(conditions, fmt.Sprintf("created_at >= $%d", argIndex))
		args = append(args, req.StartDate)
		argIndex++
	}

	if req.EndDate != "" {
		conditions = append(conditions, fmt.Sprintf("created_at <= $%d", argIndex))
		args = append(args, req.EndDate)
		argIndex++
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + fmt.Sprintf("%s", conditions[0])
		for i := 1; i < len(conditions); i++ {
			whereClause += " AND " + conditions[i]
		}
	}

	// Get total count
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM activities %s", whereClause)
	var total int
	err := s.db.Postgres.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get activities count: %w", err)
	}

	// Set defaults
	page := req.Page
	if page < 1 {
		page = 1
	}
	limit := req.Limit
	if limit < 1 {
		limit = 20
	}

	offset := (page - 1) * limit

	// Get activities
	query := fmt.Sprintf(`
		SELECT id, type, title, description, entity_type, entity_id, entity_name,
			   user_id, user_name, metadata, created_at
		FROM activities
		%s
		ORDER BY created_at DESC
		LIMIT $%d OFFSET $%d
	`, whereClause, argIndex, argIndex+1)

	args = append(args, limit, offset)

	rows, err := s.db.Postgres.Query(query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query activities: %w", err)
	}
	defer rows.Close()

	var activities []*models.Activity
	for rows.Next() {
		activity := &models.Activity{}
		err := rows.Scan(
			&activity.ID, &activity.Type, &activity.Title, &activity.Description,
			&activity.EntityType, &activity.EntityID, &activity.EntityName,
			&activity.UserID, &activity.UserName, &activity.Metadata, &activity.CreatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan activity: %w", err)
		}
		activities = append(activities, activity)
	}

	if err = rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("failed to iterate activities: %w", err)
	}

	return activities, total, nil
}

// LogActivity is a helper function to quickly log an activity
func (s *ActivityService) LogActivity(activityType models.ActivityType, title, description, entityType string, entityID *uuid.UUID, entityName *string, userID *uuid.UUID, userName *string, metadata map[string]interface{}) error {
	req := &models.CreateActivityRequest{
		Type:        activityType,
		Title:       title,
		Description: description,
		EntityType:  entityType,
		EntityID:    entityID,
		EntityName:  entityName,
		UserID:      userID,
		UserName:    userName,
		Metadata:    metadata,
	}

	_, err := s.CreateActivity(req)
	return err
}

// GetRecentActivities gets the most recent activities (convenience method)
func (s *ActivityService) GetRecentActivities(limit int) ([]*models.Activity, error) {
	if limit <= 0 {
		limit = 10
	}

	req := &models.ActivityListRequest{
		Page:  1,
		Limit: limit,
	}

	activities, _, err := s.GetActivities(req)
	return activities, err
}
