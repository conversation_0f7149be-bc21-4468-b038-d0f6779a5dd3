package services

import (
	"database/sql"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/anandjewels/jewelry-backend/internal/database"
	"github.com/anandjewels/jewelry-backend/internal/models"
	"github.com/google/uuid"
)

// CollectionService handles business logic for collections
type CollectionService struct {
	db *database.DB
}

// NewCollectionService creates a new collection service
func NewCollectionService(db *database.DB) *CollectionService {
	return &CollectionService{db: db}
}

// CreateCollection creates a new collection
func (s *CollectionService) CreateCollection(req *models.CreateCollectionRequest) (*models.Collection, error) {
	collection := &models.Collection{
		ID:            uuid.New(),
		Slug:          req.Slug,
		Name:          req.Name,
		Description:   req.Description,
		CoverImageURL: req.CoverImageURL,
		IsActive:      true,
		IsPublic:      req.IsPublic,
		ExpiresAt:     req.ExpiresAt,
		ViewCount:     0,
		ShareCount:    0,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	query := `
		INSERT INTO collections (
			id, slug, name, description, cover_image_url, is_active, is_public,
			expires_at, view_count, share_count, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
		)`

	_, err := s.db.Postgres.Exec(query,
		collection.ID, collection.Slug, collection.Name, collection.Description,
		collection.CoverImageURL, collection.IsActive, collection.IsPublic,
		collection.ExpiresAt, collection.ViewCount, collection.ShareCount,
		collection.CreatedAt, collection.UpdatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to create collection: %w", err)
	}

	return collection, nil
}

// GetCollections retrieves collections with filtering and pagination
func (s *CollectionService) GetCollections(req *models.CollectionListRequest) ([]*models.Collection, int, error) {
	// Build WHERE clause
	var conditions []string
	var args []interface{}
	argIndex := 1

	if req.IsActive != nil {
		conditions = append(conditions, fmt.Sprintf("is_active = $%d", argIndex))
		args = append(args, *req.IsActive)
		argIndex++
	}

	if req.IsPublic != nil {
		conditions = append(conditions, fmt.Sprintf("is_public = $%d", argIndex))
		args = append(args, *req.IsPublic)
		argIndex++
	}

	if req.Search != nil && *req.Search != "" {
		conditions = append(conditions, fmt.Sprintf("(name ILIKE $%d OR description ILIKE $%d)", argIndex, argIndex))
		args = append(args, "%"+*req.Search+"%")
		argIndex++
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// Count total records
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM collections %s", whereClause)
	var total int
	err := s.db.Postgres.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count collections: %w", err)
	}

	// Build ORDER BY clause
	orderBy := "created_at DESC"
	if req.SortBy != "" {
		validSortFields := map[string]bool{
			"name":       true,
			"created_at": true,
			"updated_at": true,
			"view_count": true,
		}
		if validSortFields[req.SortBy] {
			direction := "ASC"
			if req.SortOrder == "desc" {
				direction = "DESC"
			}
			orderBy = fmt.Sprintf("%s %s", req.SortBy, direction)
		}
	}

	// Calculate offset
	offset := (req.Page - 1) * req.Limit

	// Query collections with product count
	query := fmt.Sprintf(`
		SELECT c.id, c.slug, c.name, c.description, c.cover_image_url, c.is_active,
			   c.is_public, c.expires_at, c.view_count, c.share_count,
			   c.created_at, c.updated_at,
			   COALESCE(cp_count.product_count, 0) as product_count
		FROM collections c
		LEFT JOIN (
			SELECT collection_id, COUNT(*) as product_count
			FROM collection_products
			GROUP BY collection_id
		) cp_count ON c.id = cp_count.collection_id
		%s
		ORDER BY %s
		LIMIT $%d OFFSET $%d
	`, whereClause, orderBy, argIndex, argIndex+1)

	args = append(args, req.Limit, offset)

	rows, err := s.db.Postgres.Query(query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query collections: %w", err)
	}
	defer rows.Close()

	var collections []*models.Collection
	for rows.Next() {
		collection := &models.Collection{}
		err := rows.Scan(
			&collection.ID, &collection.Slug, &collection.Name, &collection.Description,
			&collection.CoverImageURL, &collection.IsActive, &collection.IsPublic,
			&collection.ExpiresAt, &collection.ViewCount, &collection.ShareCount,
			&collection.CreatedAt, &collection.UpdatedAt, &collection.ProductCount,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan collection: %w", err)
		}
		collections = append(collections, collection)
	}

	if err = rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("error iterating collections: %w", err)
	}

	return collections, total, nil
}

// GetCollectionByID retrieves a single collection by ID
func (s *CollectionService) GetCollectionByID(id uuid.UUID) (*models.Collection, error) {
	query := `
		SELECT c.id, c.slug, c.name, c.description, c.cover_image_url, c.is_active,
			   c.is_public, c.expires_at, c.view_count, c.share_count,
			   c.created_at, c.updated_at,
			   COALESCE(cp_count.product_count, 0) as product_count
		FROM collections c
		LEFT JOIN (
			SELECT collection_id, COUNT(*) as product_count
			FROM collection_products
			GROUP BY collection_id
		) cp_count ON c.id = cp_count.collection_id
		WHERE c.id = $1
	`

	collection := &models.Collection{}
	err := s.db.Postgres.QueryRow(query, id).Scan(
		&collection.ID, &collection.Slug, &collection.Name, &collection.Description,
		&collection.CoverImageURL, &collection.IsActive, &collection.IsPublic,
		&collection.ExpiresAt, &collection.ViewCount, &collection.ShareCount,
		&collection.CreatedAt, &collection.UpdatedAt, &collection.ProductCount,
	)

	if err != nil {
		return nil, err
	}

	return collection, nil
}

// GetCollectionBySlug retrieves a single collection by slug
func (s *CollectionService) GetCollectionBySlug(slug string) (*models.Collection, error) {
	query := `
		SELECT c.id, c.slug, c.name, c.description, c.cover_image_url, c.is_active,
			   c.is_public, c.expires_at, c.view_count, c.share_count,
			   c.created_at, c.updated_at,
			   COALESCE(cp_count.product_count, 0) as product_count
		FROM collections c
		LEFT JOIN (
			SELECT collection_id, COUNT(*) as product_count
			FROM collection_products
			GROUP BY collection_id
		) cp_count ON c.id = cp_count.collection_id
		WHERE c.slug = $1
	`

	collection := &models.Collection{}
	err := s.db.Postgres.QueryRow(query, slug).Scan(
		&collection.ID, &collection.Slug, &collection.Name, &collection.Description,
		&collection.CoverImageURL, &collection.IsActive, &collection.IsPublic,
		&collection.ExpiresAt, &collection.ViewCount, &collection.ShareCount,
		&collection.CreatedAt, &collection.UpdatedAt, &collection.ProductCount,
	)

	if err != nil {
		return nil, err
	}

	return collection, nil
}

// GetCollectionWithProducts retrieves a collection with its products
func (s *CollectionService) GetCollectionWithProducts(id uuid.UUID) (*models.Collection, error) {
	// Get collection
	collection, err := s.GetCollectionByID(id)
	if err != nil {
		return nil, err
	}

	// Get products in collection
	query := `
		SELECT p.id, p.sku, p.name, p.description, p.category, p.subcategory,
			   p.weight, p.net_weight, p.material, p.metal_purity, p.availability,
			   p.stock_quantity, p.min_stock_level, p.tags, p.is_featured, p.is_active,
			   p.created_at, p.updated_at, cp.display_order, cp.is_featured as collection_featured
		FROM products p
		INNER JOIN collection_products cp ON p.id = cp.product_id
		WHERE cp.collection_id = $1
		ORDER BY cp.display_order ASC, cp.added_at ASC
	`

	rows, err := s.db.Postgres.Query(query, id)
	if err != nil {
		return nil, fmt.Errorf("failed to query collection products: %w", err)
	}
	defer rows.Close()

	var products []models.Product
	for rows.Next() {
		product := models.Product{}
		var displayOrder int
		var collectionFeatured bool

		err := rows.Scan(
			&product.ID, &product.SKU, &product.Name, &product.Description,
			&product.Category, &product.Subcategory,
			&product.Weight, &product.NetWeight, &product.Material, &product.MetalPurity,
			&product.Availability, &product.StockQuantity,
			&product.MinStockLevel, &product.Tags, &product.IsFeatured, &product.IsActive,
			&product.CreatedAt, &product.UpdatedAt, &displayOrder, &collectionFeatured,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan collection product: %w", err)
		}
		products = append(products, product)
	}

	if err = rows.Err(); err != nil {
		return nil, fmt.Errorf("error iterating collection products: %w", err)
	}

	collection.Products = products
	return collection, nil
}

// AddProductToCollection adds a product to a collection
func (s *CollectionService) AddProductToCollection(collectionID, productID uuid.UUID, displayOrder int, isFeatured bool) error {
	// Check if product already exists in collection
	var exists bool
	checkQuery := "SELECT EXISTS(SELECT 1 FROM collection_products WHERE collection_id = $1 AND product_id = $2)"
	err := s.db.Postgres.QueryRow(checkQuery, collectionID, productID).Scan(&exists)
	if err != nil {
		return fmt.Errorf("failed to check product existence in collection: %w", err)
	}

	if exists {
		return fmt.Errorf("product already exists in collection")
	}

	// Add product to collection
	query := `
		INSERT INTO collection_products (id, collection_id, product_id, display_order, is_featured, added_at)
		VALUES ($1, $2, $3, $4, $5, $6)
	`

	_, err = s.db.Postgres.Exec(query,
		uuid.New(), collectionID, productID, displayOrder, isFeatured, time.Now(),
	)

	if err != nil {
		return fmt.Errorf("failed to add product to collection: %w", err)
	}

	// Update collection cover image if this is the first product
	err = s.updateCollectionCoverImage(collectionID)
	if err != nil {
		// Log error but don't fail the operation
		fmt.Printf("Warning: failed to update collection cover image: %v\n", err)
	}

	return nil
}

// RemoveProductFromCollection removes a product from a collection
func (s *CollectionService) RemoveProductFromCollection(collectionID, productID uuid.UUID) error {
	query := "DELETE FROM collection_products WHERE collection_id = $1 AND product_id = $2"

	result, err := s.db.Postgres.Exec(query, collectionID, productID)
	if err != nil {
		return fmt.Errorf("failed to remove product from collection: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("product not found in collection")
	}

	return nil
}

// UpdateCollection updates a collection
func (s *CollectionService) UpdateCollection(id uuid.UUID, req *models.UpdateCollectionRequest) (*models.Collection, error) {
	// Build update query dynamically
	var setParts []string
	var args []interface{}
	argIndex := 1

	if req.Name != nil {
		setParts = append(setParts, fmt.Sprintf("name = $%d", argIndex))
		args = append(args, *req.Name)
		argIndex++
	}

	if req.Description != nil {
		setParts = append(setParts, fmt.Sprintf("description = $%d", argIndex))
		args = append(args, *req.Description)
		argIndex++
	}

	if req.CoverImageURL != nil {
		setParts = append(setParts, fmt.Sprintf("cover_image_url = $%d", argIndex))
		args = append(args, *req.CoverImageURL)
		argIndex++
	}

	if req.IsActive != nil {
		setParts = append(setParts, fmt.Sprintf("is_active = $%d", argIndex))
		args = append(args, *req.IsActive)
		argIndex++
	}

	if req.IsPublic != nil {
		setParts = append(setParts, fmt.Sprintf("is_public = $%d", argIndex))
		args = append(args, *req.IsPublic)
		argIndex++
	}

	if req.ExpiresAt != nil {
		setParts = append(setParts, fmt.Sprintf("expires_at = $%d", argIndex))
		args = append(args, *req.ExpiresAt)
		argIndex++
	}

	if len(setParts) == 0 {
		return s.GetCollectionByID(id)
	}

	// Add updated_at
	setParts = append(setParts, fmt.Sprintf("updated_at = $%d", argIndex))
	args = append(args, time.Now())
	argIndex++

	// Add ID for WHERE clause
	args = append(args, id)

	query := fmt.Sprintf("UPDATE collections SET %s WHERE id = $%d", strings.Join(setParts, ", "), argIndex)

	_, err := s.db.Postgres.Exec(query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to update collection: %w", err)
	}

	return s.GetCollectionByID(id)
}

// DeleteCollection deletes a collection
func (s *CollectionService) DeleteCollection(id uuid.UUID) error {
	query := "DELETE FROM collections WHERE id = $1"

	result, err := s.db.Postgres.Exec(query, id)
	if err != nil {
		return fmt.Errorf("failed to delete collection: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return fmt.Errorf("collection not found")
	}

	return nil
}

// IncrementViewCount increments the view count for a collection
func (s *CollectionService) IncrementViewCount(id uuid.UUID) error {
	query := "UPDATE collections SET view_count = view_count + 1 WHERE id = $1"
	_, err := s.db.Postgres.Exec(query, id)
	if err != nil {
		return fmt.Errorf("failed to increment view count: %w", err)
	}
	return nil
}

// IncrementShareCount increments the share count for a collection
func (s *CollectionService) IncrementShareCount(id uuid.UUID) error {
	query := "UPDATE collections SET share_count = share_count + 1 WHERE id = $1"
	_, err := s.db.Postgres.Exec(query, id)
	if err != nil {
		return fmt.Errorf("failed to increment share count: %w", err)
	}
	return nil
}

// updateCollectionCoverImage updates the collection's cover image to the first product's primary image
func (s *CollectionService) updateCollectionCoverImage(collectionID uuid.UUID) error {
	// Get the first product in the collection with its primary image
	query := `
		SELECT p.id, pi.id
		FROM collection_products cp
		INNER JOIN products p ON cp.product_id = p.id
		INNER JOIN product_images pi ON p.id = pi.product_id
		WHERE cp.collection_id = $1 AND pi.is_primary = true
		ORDER BY cp.display_order ASC, cp.added_at ASC
		LIMIT 1
	`

	var productID, imageID string
	err := s.db.Postgres.QueryRow(query, collectionID).Scan(&productID, &imageID)
	if err != nil {
		if err == sql.ErrNoRows {
			// No products with images, clear the cover image
			updateQuery := "UPDATE collections SET cover_image_url = NULL WHERE id = $1"
			_, err = s.db.Postgres.Exec(updateQuery, collectionID)
			return err
		}
		return fmt.Errorf("failed to get first product image: %w", err)
	}

	// Generate proxy URL for the image
	baseURL := os.Getenv("API_BASE_URL")
	proxyURL := fmt.Sprintf("%s/api/v1/proxy/images/%s/%s", baseURL, productID, imageID)

	// Update collection cover image
	updateQuery := "UPDATE collections SET cover_image_url = $1 WHERE id = $2"
	_, err = s.db.Postgres.Exec(updateQuery, proxyURL, collectionID)
	if err != nil {
		return fmt.Errorf("failed to update collection cover image: %w", err)
	}

	return nil
}
