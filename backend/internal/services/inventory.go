package services

import (
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/anandjewels/jewelry-backend/internal/database"
	"github.com/anandjewels/jewelry-backend/internal/models"
	"github.com/google/uuid"
)

// InventoryService handles business logic for inventory management
type InventoryService struct {
	db             *database.DB
	productService *ProductService
}

// NewInventoryService creates a new inventory service
func NewInventoryService(db *database.DB) *InventoryService {
	return &InventoryService{
		db:             db,
		productService: NewProductService(db),
	}
}

// UpdateInventory updates the inventory for a product
func (s *InventoryService) UpdateInventory(productID uuid.UUID, req *models.UpdateInventoryRequest) (*models.InventoryLog, error) {
	// Start transaction
	tx, err := s.db.Postgres.Begin()
	if err != nil {
		return nil, fmt.Errorf("failed to start transaction: %w", err)
	}
	defer tx.Rollback()

	// Get current product stock
	var currentStock int
	err = tx.QueryRow("SELECT stock_quantity FROM products WHERE id = $1", productID).Scan(&currentStock)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("product not found")
		}
		return nil, fmt.Errorf("failed to get current stock: %w", err)
	}

	// Calculate new stock based on change type
	var newStock int
	var quantityChange int

	switch models.InventoryChangeType(req.ChangeType) {
	case models.InventoryChangeIncrease:
		newStock = currentStock + req.Quantity
		quantityChange = req.Quantity
	case models.InventoryChangeDecrease:
		newStock = currentStock - req.Quantity
		quantityChange = -req.Quantity
		if newStock < 0 {
			return nil, fmt.Errorf("insufficient stock: current %d, requested decrease %d", currentStock, req.Quantity)
		}
	case models.InventoryChangeAdjust:
		newStock = req.Quantity
		quantityChange = req.Quantity - currentStock
	default:
		return nil, fmt.Errorf("invalid change type: %s", req.ChangeType)
	}

	// Update product stock
	_, err = tx.Exec("UPDATE products SET stock_quantity = $1, updated_at = $2 WHERE id = $3",
		newStock, time.Now(), productID)
	if err != nil {
		return nil, fmt.Errorf("failed to update product stock: %w", err)
	}

	// Create inventory log entry
	log := &models.InventoryLog{
		ID:             uuid.New(),
		ProductID:      productID,
		ChangeType:     models.InventoryChangeType(req.ChangeType),
		QuantityChange: quantityChange,
		PreviousStock:  currentStock,
		NewStock:       newStock,
		Reason:         req.Reason,
		CreatedAt:      time.Now(),
	}

	// Insert inventory log
	_, err = tx.Exec(`
		INSERT INTO inventory_logs (id, product_id, change_type, quantity_change, quantity_before, quantity_after, reason, created_at)
		VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
	`, log.ID, log.ProductID, log.ChangeType, log.QuantityChange, log.PreviousStock, log.NewStock, log.Reason, log.CreatedAt)
	if err != nil {
		return nil, fmt.Errorf("failed to create inventory log: %w", err)
	}

	// Commit transaction
	if err = tx.Commit(); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return log, nil
}

// GetInventoryStatus retrieves inventory status with filtering and pagination
func (s *InventoryService) GetInventoryStatus(req *models.InventoryListRequest) ([]*models.InventoryStatus, int, error) {
	// Build WHERE clause
	var conditions []string
	var args []interface{}
	argIndex := 1

	if req.Category != nil {
		conditions = append(conditions, fmt.Sprintf("p.category = $%d", argIndex))
		args = append(args, *req.Category)
		argIndex++
	}

	if req.LowStockOnly {
		conditions = append(conditions, "p.stock_quantity <= p.min_stock_level")
	}

	if req.OutOfStock {
		conditions = append(conditions, "p.stock_quantity = 0")
	}

	if req.Search != nil && *req.Search != "" {
		conditions = append(conditions, fmt.Sprintf("(p.name ILIKE $%d OR p.sku ILIKE $%d)", argIndex, argIndex))
		args = append(args, "%"+*req.Search+"%")
		argIndex++
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// Count total records
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM products p %s", whereClause)
	var total int
	err := s.db.Postgres.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count inventory: %w", err)
	}

	// Build ORDER BY clause
	orderBy := "p.updated_at DESC"
	if req.SortBy != "" {
		validSortFields := map[string]bool{
			"name":         true,
			"sku":          true,
			"stock":        true,
			"last_updated": true,
		}
		if validSortFields[req.SortBy] {
			direction := "ASC"
			if req.SortOrder == "desc" {
				direction = "DESC"
			}

			sortField := req.SortBy
			if req.SortBy == "stock" {
				sortField = "stock_quantity"
			} else if req.SortBy == "last_updated" {
				sortField = "updated_at"
			}

			orderBy = fmt.Sprintf("p.%s %s", sortField, direction)
		}
	}

	// Calculate offset
	offset := (req.Page - 1) * req.Limit

	// Query inventory status
	query := fmt.Sprintf(`
		SELECT p.id, p.name, p.sku, p.stock_quantity, p.min_stock_level, p.updated_at,
			   (p.stock_quantity <= p.min_stock_level) as is_low_stock,
			   (p.stock_quantity = 0) as is_out_of_stock
		FROM products p %s
		ORDER BY %s
		LIMIT $%d OFFSET $%d
	`, whereClause, orderBy, argIndex, argIndex+1)

	args = append(args, req.Limit, offset)

	rows, err := s.db.Postgres.Query(query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query inventory: %w", err)
	}
	defer rows.Close()

	var inventory []*models.InventoryStatus
	for rows.Next() {
		status := &models.InventoryStatus{}
		err := rows.Scan(
			&status.ProductID, &status.ProductName, &status.ProductSKU,
			&status.CurrentStock, &status.MinStockLevel, &status.LastUpdated,
			&status.IsLowStock, &status.IsOutOfStock,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan inventory status: %w", err)
		}
		inventory = append(inventory, status)
	}

	if err = rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("error iterating inventory: %w", err)
	}

	return inventory, total, nil
}

// GetInventoryLogs retrieves inventory change logs with filtering and pagination
func (s *InventoryService) GetInventoryLogs(req *models.InventoryLogListRequest) ([]*models.InventoryLog, int, error) {
	// Build WHERE clause
	var conditions []string
	var args []interface{}
	argIndex := 1

	if req.ProductID != nil {
		conditions = append(conditions, fmt.Sprintf("il.product_id = $%d", argIndex))
		args = append(args, *req.ProductID)
		argIndex++
	}

	if req.DateFrom != nil {
		conditions = append(conditions, fmt.Sprintf("il.created_at >= $%d", argIndex))
		args = append(args, *req.DateFrom)
		argIndex++
	}

	if req.DateTo != nil {
		conditions = append(conditions, fmt.Sprintf("il.created_at <= $%d", argIndex))
		args = append(args, *req.DateTo)
		argIndex++
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// Count total records
	countQuery := fmt.Sprintf(`
		SELECT COUNT(*) 
		FROM inventory_logs il 
		LEFT JOIN products p ON il.product_id = p.id 
		%s
	`, whereClause)

	var total int
	err := s.db.Postgres.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count inventory logs: %w", err)
	}

	// Build ORDER BY clause
	orderBy := "il.created_at DESC"
	if req.SortBy != "" {
		validSortFields := map[string]bool{
			"created_at":      true,
			"quantity_change": true,
		}
		if validSortFields[req.SortBy] {
			direction := "ASC"
			if req.SortOrder == "desc" {
				direction = "DESC"
			}
			orderBy = fmt.Sprintf("il.%s %s", req.SortBy, direction)
		}
	}

	// Calculate offset
	offset := (req.Page - 1) * req.Limit

	// Query inventory logs with product information
	query := fmt.Sprintf(`
		SELECT il.id, il.product_id, il.change_type, il.quantity_change, il.quantity_before,
			   il.quantity_after, il.reason, il.created_at,
			   p.name, p.sku, p.description, p.category, p.subcategory,
			   p.weight, p.net_weight, p.material, p.metal_purity,
			   p.availability, p.stock_quantity, p.tags, p.is_featured, p.is_active,
			   p.created_at as product_created_at, p.updated_at as product_updated_at
		FROM inventory_logs il
		LEFT JOIN products p ON il.product_id = p.id
		%s
		ORDER BY %s
		LIMIT $%d OFFSET $%d
	`, whereClause, orderBy, argIndex, argIndex+1)

	args = append(args, req.Limit, offset)

	rows, err := s.db.Postgres.Query(query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query inventory logs: %w", err)
	}
	defer rows.Close()

	var logs []*models.InventoryLog
	for rows.Next() {
		log := &models.InventoryLog{}
		product := &models.Product{}

		err := rows.Scan(
			&log.ID, &log.ProductID, &log.ChangeType, &log.QuantityChange,
			&log.PreviousStock, &log.NewStock, &log.Reason, &log.CreatedAt,
			&product.Name, &product.SKU, &product.Description,
			&product.Category, &product.Subcategory, &product.Weight, &product.NetWeight,
			&product.Material, &product.MetalPurity,
			&product.Availability, &product.StockQuantity,
			&product.Tags, &product.IsFeatured, &product.IsActive,
			&product.CreatedAt, &product.UpdatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan inventory log: %w", err)
		}

		product.ID = log.ProductID
		log.Product = product
		logs = append(logs, log)
	}

	if err = rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("error iterating inventory logs: %w", err)
	}

	return logs, total, nil
}
