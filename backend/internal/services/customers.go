package services

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/anandjewels/jewelry-backend/internal/database"
	"github.com/anandjewels/jewelry-backend/internal/models"
	"github.com/google/uuid"
)

// CustomerService handles business logic for customers
type CustomerService struct {
	db *database.DB
}

// NewCustomerService creates a new customer service
func NewCustomerService(db *database.DB) *CustomerService {
	return &CustomerService{db: db}
}

// CreateCustomer creates a new customer
func (s *CustomerService) CreateCustomer(req *models.CreateCustomerRequest) (*models.Customer, error) {
	customer := &models.Customer{
		ID:              uuid.New(),
		Name:            req.Name,
		Email:           req.Email,
		Phone:           req.Phone,
		AlternatePhone:  req.AlternatePhone,
		TotalOrders:     0,
		TotalSpent:      0.0,
		PreferredMethod: req.PreferredMethod,
		Notes:           req.Notes,
		IsActive:        true,
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// Marshal address to JSON if provided
	if req.Address != nil {
		addressJSON, err := json.Marshal(req.Address)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal address: %w", err)
		}
		customer.Address = addressJSON
	}

	query := `
		INSERT INTO customers (
			id, name, email, phone, address, total_orders,
			total_spent, notes, created_at, updated_at
		) VALUES (
			$1, $2, $3, $4, $5, $6, $7, $8, $9, $10
		)`

	_, err := s.db.Postgres.Exec(query,
		customer.ID, customer.Name, customer.Email, customer.Phone,
		customer.Address, customer.TotalOrders, customer.TotalSpent,
		customer.Notes, customer.CreatedAt, customer.UpdatedAt,
	)

	if err != nil {
		return nil, fmt.Errorf("failed to create customer: %w", err)
	}

	return customer, nil
}

// GetCustomers retrieves customers with filtering and pagination
func (s *CustomerService) GetCustomers(req *models.CustomerListRequest) ([]*models.Customer, int, error) {
	// Build WHERE clause
	var conditions []string
	var args []interface{}
	argIndex := 1

	if req.IsActive != nil {
		conditions = append(conditions, fmt.Sprintf("is_active = $%d", argIndex))
		args = append(args, *req.IsActive)
		argIndex++
	}

	if req.Search != nil && *req.Search != "" {
		conditions = append(conditions, fmt.Sprintf("(name ILIKE $%d OR email ILIKE $%d OR phone ILIKE $%d)", argIndex, argIndex, argIndex))
		args = append(args, "%"+*req.Search+"%")
		argIndex++
	}

	whereClause := ""
	if len(conditions) > 0 {
		whereClause = "WHERE " + strings.Join(conditions, " AND ")
	}

	// Count total records
	countQuery := fmt.Sprintf("SELECT COUNT(*) FROM customers %s", whereClause)
	var total int
	err := s.db.Postgres.QueryRow(countQuery, args...).Scan(&total)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count customers: %w", err)
	}

	// Build ORDER BY clause
	orderBy := "created_at DESC"
	if req.SortBy != "" {
		validSortFields := map[string]bool{
			"name":         true,
			"email":        true,
			"total_orders": true,
			"total_spent":  true,
			"created_at":   true,
		}
		if validSortFields[req.SortBy] {
			direction := "ASC"
			if req.SortOrder == "desc" {
				direction = "DESC"
			}
			orderBy = fmt.Sprintf("%s %s", req.SortBy, direction)
		}
	}

	// Calculate offset
	offset := (req.Page - 1) * req.Limit

	// Query customers
	query := fmt.Sprintf(`
		SELECT id, name, email, phone, address, total_orders,
			   total_spent, last_order_at, notes,
			   created_at, updated_at
		FROM customers %s
		ORDER BY %s
		LIMIT $%d OFFSET $%d
	`, whereClause, orderBy, argIndex, argIndex+1)

	args = append(args, req.Limit, offset)

	rows, err := s.db.Postgres.Query(query, args...)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to query customers: %w", err)
	}
	defer rows.Close()

	var customers []*models.Customer
	for rows.Next() {
		customer := &models.Customer{}
		err := rows.Scan(
			&customer.ID, &customer.Name, &customer.Email, &customer.Phone,
			&customer.Address, &customer.TotalOrders, &customer.TotalSpent,
			&customer.LastOrderDate, &customer.Notes, &customer.CreatedAt, &customer.UpdatedAt,
		)
		if err != nil {
			return nil, 0, fmt.Errorf("failed to scan customer: %w", err)
		}
		// Set default values for missing fields
		customer.IsActive = true
		customer.PreferredMethod = "phone"
		customers = append(customers, customer)
	}

	if err = rows.Err(); err != nil {
		return nil, 0, fmt.Errorf("error iterating customers: %w", err)
	}

	return customers, total, nil
}

// GetCustomerByID retrieves a single customer by ID
func (s *CustomerService) GetCustomerByID(id uuid.UUID) (*models.Customer, error) {
	query := `
		SELECT id, name, email, phone, address, total_orders,
			   total_spent, last_order_at, notes,
			   created_at, updated_at
		FROM customers
		WHERE id = $1
	`

	customer := &models.Customer{}
	err := s.db.Postgres.QueryRow(query, id).Scan(
		&customer.ID, &customer.Name, &customer.Email, &customer.Phone,
		&customer.Address, &customer.TotalOrders, &customer.TotalSpent,
		&customer.LastOrderDate, &customer.Notes, &customer.CreatedAt, &customer.UpdatedAt,
	)

	if err != nil {
		return nil, err
	}

	// Set default values for missing fields
	customer.IsActive = true
	customer.PreferredMethod = "phone"

	return customer, nil
}

// UpdateCustomer updates a customer
func (s *CustomerService) UpdateCustomer(id uuid.UUID, req *models.UpdateCustomerRequest) (*models.Customer, error) {
	// Build update query dynamically
	var setParts []string
	var args []interface{}
	argIndex := 1

	if req.Name != nil {
		setParts = append(setParts, fmt.Sprintf("name = $%d", argIndex))
		args = append(args, *req.Name)
		argIndex++
	}

	if req.Email != nil {
		setParts = append(setParts, fmt.Sprintf("email = $%d", argIndex))
		args = append(args, *req.Email)
		argIndex++
	}

	if req.Phone != nil {
		setParts = append(setParts, fmt.Sprintf("phone = $%d", argIndex))
		args = append(args, *req.Phone)
		argIndex++
	}

	if req.Address != nil {
		addressJSON, err := json.Marshal(req.Address)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal address: %w", err)
		}
		setParts = append(setParts, fmt.Sprintf("address = $%d", argIndex))
		args = append(args, addressJSON)
		argIndex++
	}

	if req.Notes != nil {
		setParts = append(setParts, fmt.Sprintf("notes = $%d", argIndex))
		args = append(args, *req.Notes)
		argIndex++
	}

	if len(setParts) == 0 {
		return s.GetCustomerByID(id)
	}

	// Add updated_at
	setParts = append(setParts, fmt.Sprintf("updated_at = $%d", argIndex))
	args = append(args, time.Now())
	argIndex++

	// Add ID for WHERE clause
	args = append(args, id)

	query := fmt.Sprintf("UPDATE customers SET %s WHERE id = $%d", strings.Join(setParts, ", "), argIndex)

	_, err := s.db.Postgres.Exec(query, args...)
	if err != nil {
		return nil, fmt.Errorf("failed to update customer: %w", err)
	}

	return s.GetCustomerByID(id)
}
