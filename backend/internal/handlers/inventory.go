package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/anandjewels/jewelry-backend/internal/database"
	"github.com/anandjewels/jewelry-backend/internal/middleware"
	"github.com/anandjewels/jewelry-backend/internal/models"
	"github.com/anandjewels/jewelry-backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// InventoryHandler handles inventory-related HTTP requests
type InventoryHandler struct {
	db               *database.DB
	inventoryService *services.InventoryService
}

// NewInventoryHandler creates a new inventory handler
func NewInventoryHandler(db *database.DB) *InventoryHandler {
	return &InventoryHandler{
		db:               db,
		inventoryService: services.NewInventoryService(db),
	}
}

// UpdateInventory updates inventory for a product
// @Summary Update product inventory
// @Description Update inventory quantity for a specific product
// @Tags inventory
// @Accept json
// @Produce json
// @Param id path string true "Product ID"
// @Param inventory body models.UpdateInventoryRequest true "Inventory update data"
// @Success 200 {object} models.InventoryLogResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/inventory/{id} [put]
func (h *InventoryHandler) UpdateInventory(c *gin.Context) {
	idStr := c.Param("id")
	productID, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid product ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	var req models.UpdateInventoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_request",
			Message: "Invalid request body",
			Code:    http.StatusBadRequest,
			Details: err.Error(),
		})
		return
	}

	log, err := h.inventoryService.UpdateInventory(productID, &req)
	if err != nil {
		if err.Error() == "product not found" {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error:   "not_found",
				Message: "Product not found",
				Code:    http.StatusNotFound,
			})
			return
		}
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "update_failed",
			Message: "Failed to update inventory",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, log.ToResponse())
}

// GetInventoryStatus retrieves inventory status with filtering and pagination
// @Summary Get inventory status
// @Description Get inventory status for all products with optional filtering
// @Tags inventory
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param category query string false "Filter by category"
// @Param low_stock_only query bool false "Show only low stock items"
// @Param out_of_stock query bool false "Show only out of stock items"
// @Param search query string false "Search in product name and SKU"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/inventory [get]
func (h *InventoryHandler) GetInventoryStatus(c *gin.Context) {
	// Parse query parameters
	req := &models.InventoryListRequest{
		Page:  1,
		Limit: 20,
	}

	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			req.Page = p
		}
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil && l > 0 && l <= 100 {
			req.Limit = l
		}
	}

	if category := c.Query("category"); category != "" {
		req.Category = &category
	}

	if lowStockOnly := c.Query("low_stock_only"); lowStockOnly != "" {
		if lowStock, err := strconv.ParseBool(lowStockOnly); err == nil {
			req.LowStockOnly = lowStock
		}
	}

	if outOfStock := c.Query("out_of_stock"); outOfStock != "" {
		if outStock, err := strconv.ParseBool(outOfStock); err == nil {
			req.OutOfStock = outStock
		}
	}

	if search := c.Query("search"); search != "" {
		req.Search = &search
	}

	if sortBy := c.Query("sort_by"); sortBy != "" {
		req.SortBy = sortBy
	}

	if sortOrder := c.Query("sort_order"); sortOrder != "" {
		req.SortOrder = sortOrder
	}

	inventory, total, err := h.inventoryService.GetInventoryStatus(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "fetch_failed",
			Message: "Failed to fetch inventory status",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	// Convert to response format
	inventoryResponses := make([]*models.InventoryResponse, len(inventory))
	for i, status := range inventory {
		inventoryResponses[i] = status.ToResponse()
	}

	c.Header("X-Total-Count", strconv.Itoa(total))
	c.JSON(http.StatusOK, gin.H{
		"inventory": inventoryResponses,
		"total":     total,
		"page":      req.Page,
		"limit":     req.Limit,
	})
}

// GetInventoryLogs retrieves inventory change logs with filtering and pagination
// @Summary Get inventory logs
// @Description Get inventory change logs with optional filtering
// @Tags inventory
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param product_id query string false "Filter by product ID"
// @Param date_from query string false "Filter logs from date (YYYY-MM-DD)"
// @Param date_to query string false "Filter logs to date (YYYY-MM-DD)"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/inventory/logs [get]
func (h *InventoryHandler) GetInventoryLogs(c *gin.Context) {
	// Parse query parameters
	req := &models.InventoryLogListRequest{
		Page:  1,
		Limit: 20,
	}

	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			req.Page = p
		}
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil && l > 0 && l <= 100 {
			req.Limit = l
		}
	}

	if productID := c.Query("product_id"); productID != "" {
		if id, err := uuid.Parse(productID); err == nil {
			req.ProductID = &id
		}
	}

	if dateFrom := c.Query("date_from"); dateFrom != "" {
		if date, err := time.Parse("2006-01-02", dateFrom); err == nil {
			req.DateFrom = &date
		}
	}

	if dateTo := c.Query("date_to"); dateTo != "" {
		if date, err := time.Parse("2006-01-02", dateTo); err == nil {
			// Set to end of day
			endOfDay := date.Add(23*time.Hour + 59*time.Minute + 59*time.Second)
			req.DateTo = &endOfDay
		}
	}

	if sortBy := c.Query("sort_by"); sortBy != "" {
		req.SortBy = sortBy
	}

	if sortOrder := c.Query("sort_order"); sortOrder != "" {
		req.SortOrder = sortOrder
	}

	logs, total, err := h.inventoryService.GetInventoryLogs(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "fetch_failed",
			Message: "Failed to fetch inventory logs",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	// Convert to response format
	logResponses := make([]*models.InventoryLogResponse, len(logs))
	for i, log := range logs {
		logResponses[i] = log.ToResponse()
	}

	c.Header("X-Total-Count", strconv.Itoa(total))
	c.JSON(http.StatusOK, gin.H{
		"logs":  logResponses,
		"total": total,
		"page":  req.Page,
		"limit": req.Limit,
	})
}

// GetLowStockAlerts retrieves products with low stock
// @Summary Get low stock alerts
// @Description Get products that are at or below minimum stock level
// @Tags inventory
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/inventory/alerts/low-stock [get]
func (h *InventoryHandler) GetLowStockAlerts(c *gin.Context) {
	req := &models.InventoryListRequest{
		Page:         1,
		Limit:        100, // Get all low stock items
		LowStockOnly: true,
		SortBy:       "stock",
		SortOrder:    "asc",
	}

	inventory, total, err := h.inventoryService.GetInventoryStatus(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "fetch_failed",
			Message: "Failed to fetch low stock alerts",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	// Convert to response format
	inventoryResponses := make([]*models.InventoryResponse, len(inventory))
	for i, status := range inventory {
		inventoryResponses[i] = status.ToResponse()
	}

	c.JSON(http.StatusOK, gin.H{
		"alerts": inventoryResponses,
		"total":  total,
	})
}

// GetOutOfStockAlerts retrieves products that are out of stock
// @Summary Get out of stock alerts
// @Description Get products that are completely out of stock
// @Tags inventory
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/inventory/alerts/out-of-stock [get]
func (h *InventoryHandler) GetOutOfStockAlerts(c *gin.Context) {
	req := &models.InventoryListRequest{
		Page:       1,
		Limit:      100, // Get all out of stock items
		OutOfStock: true,
		SortBy:     "last_updated",
		SortOrder:  "desc",
	}

	inventory, total, err := h.inventoryService.GetInventoryStatus(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "fetch_failed",
			Message: "Failed to fetch out of stock alerts",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	// Convert to response format
	inventoryResponses := make([]*models.InventoryResponse, len(inventory))
	for i, status := range inventory {
		inventoryResponses[i] = status.ToResponse()
	}

	c.JSON(http.StatusOK, gin.H{
		"alerts": inventoryResponses,
		"total":  total,
	})
}
