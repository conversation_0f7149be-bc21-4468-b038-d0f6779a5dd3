package handlers

import (
	"net/http"
	"strconv"

	"github.com/anandjewels/jewelry-backend/internal/database"
	"github.com/anandjewels/jewelry-backend/internal/middleware"
	"github.com/anandjewels/jewelry-backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// ImageServingHandler handles optimized image serving and CDN functionality
type ImageServingHandler struct {
	db                  *database.DB
	imageServingService *services.ImageServingService
}

// NewImageServingHandler creates a new image serving handler
func NewImageServingHandler(db *database.DB) *ImageServingHandler {
	return &ImageServingHandler{
		db:                  db,
		imageServingService: services.NewImageServingService(),
	}
}

// GetOptimizedImageURL generates optimized image URLs for a product image
// @Summary Get optimized image URL
// @Description Generate optimized image URLs with CDN support and responsive variants
// @Tags image-serving
// @Accept json
// @Produce json
// @Param id path string true "Product ID"
// @Param image_id path string true "Image ID"
// @Param size query string false "Image size" Enums(thumbnail, small, medium, large, original) default(medium)
// @Param format query string false "Image format" Enums(webp, jpeg, png, avif)
// @Param quality query int false "Image quality (1-100)" minimum(1) maximum(100) default(85)
// @Param device query string false "Device type" Enums(desktop, mobile, tablet) default(desktop)
// @Param webp query bool false "Enable WebP variant" default(true)
// @Param avif query bool false "Enable AVIF variant" default(false)
// @Param responsive query bool false "Generate responsive image set" default(false)
// @Success 200 {object} map[string]interface{}
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/images/{id}/{image_id}/optimized [get]
func (h *ImageServingHandler) GetOptimizedImageURL(c *gin.Context) {
	productIDStr := c.Param("id")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid product ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	imageIDStr := c.Param("image_id")
	imageID, err := uuid.Parse(imageIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid image ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Get image record from database
	var storagePath, imageURL string
	err = h.db.Postgres.QueryRow(`
		SELECT storage_path, image_url 
		FROM product_images 
		WHERE id = $1 AND product_id = $2
	`, imageID, productID).Scan(&storagePath, &imageURL)

	if err != nil {
		c.JSON(http.StatusNotFound, middleware.ErrorResponse{
			Error:   "not_found",
			Message: "Image not found",
			Code:    http.StatusNotFound,
		})
		return
	}

	// Parse query parameters
	options := services.ImageServingOptions{
		Size:        services.ImageSize(c.DefaultQuery("size", "medium")),
		Quality:     85,
		DeviceType:  services.DeviceType(c.DefaultQuery("device", "desktop")),
		EnableWebP:  c.DefaultQuery("webp", "true") == "true",
		EnableAVIF:  c.DefaultQuery("avif", "false") == "true",
		LazyLoading: c.DefaultQuery("lazy", "false") == "true",
	}

	// Parse quality parameter
	if qualityStr := c.Query("quality"); qualityStr != "" {
		if quality, err := strconv.Atoi(qualityStr); err == nil && quality >= 1 && quality <= 100 {
			options.Quality = quality
		}
	}

	// Parse format parameter
	if format := c.Query("format"); format != "" {
		options.Format = services.ImageFormat(format)
	} else {
		// Auto-detect optimal format based on Accept header
		options.Format = h.imageServingService.GetOptimalFormat(c.GetHeader("Accept"))
	}

	// Check if responsive image set is requested
	responsive := c.DefaultQuery("responsive", "false") == "true"

	// Set cache headers
	c.Header("Cache-Control", "public, max-age=86400") // 24 hours
	c.Header("Vary", "Accept")

	if responsive {
		// Generate responsive image set
		imageSet := h.imageServingService.GenerateResponsiveImageSet(storagePath, options)
		c.JSON(http.StatusOK, gin.H{
			"type":       "responsive_set",
			"image_set":  imageSet,
			"product_id": productID,
			"image_id":   imageID,
		})
	} else {
		// Generate single optimized URL
		optimizedURL := h.imageServingService.GenerateOptimizedURL(storagePath, options)
		c.JSON(http.StatusOK, gin.H{
			"type":          "single_image",
			"optimized_url": optimizedURL,
			"product_id":    productID,
			"image_id":      imageID,
		})
	}
}

// GetProductImageSet generates a complete image set for a product
// @Summary Get product image set
// @Description Generate optimized image URLs for all product images with responsive variants
// @Tags image-serving
// @Accept json
// @Produce json
// @Param id path string true "Product ID"
// @Param size query string false "Image size" Enums(thumbnail, small, medium, large, original) default(medium)
// @Param device query string false "Device type" Enums(desktop, mobile, tablet) default(desktop)
// @Param webp query bool false "Enable WebP variants" default(true)
// @Param avif query bool false "Enable AVIF variants" default(false)
// @Success 200 {object} map[string]interface{}
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/images/{id}/set [get]
func (h *ImageServingHandler) GetProductImageSet(c *gin.Context) {
	productIDStr := c.Param("id")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid product ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Get all images for the product
	rows, err := h.db.Postgres.Query(`
		SELECT id, storage_path, image_url, alt_text, display_order, is_primary
		FROM product_images 
		WHERE product_id = $1 
		ORDER BY display_order, created_at
	`, productID)

	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "database_error",
			Message: "Failed to fetch product images",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}
	defer rows.Close()

	// Parse query parameters
	options := services.ImageServingOptions{
		Size:        services.ImageSize(c.DefaultQuery("size", "medium")),
		DeviceType:  services.DeviceType(c.DefaultQuery("device", "desktop")),
		EnableWebP:  c.DefaultQuery("webp", "true") == "true",
		EnableAVIF:  c.DefaultQuery("avif", "false") == "true",
		LazyLoading: true, // Always enable for image sets
	}

	// Auto-detect optimal format
	options.Format = h.imageServingService.GetOptimalFormat(c.GetHeader("Accept"))

	var images []map[string]interface{}
	var primaryImage map[string]interface{}

	for rows.Next() {
		var imageID uuid.UUID
		var storagePath, imageURL, altText string
		var displayOrder int
		var isPrimary bool

		err := rows.Scan(&imageID, &storagePath, &imageURL, &altText, &displayOrder, &isPrimary)
		if err != nil {
			continue
		}

		// Generate responsive image set for each image
		imageSet := h.imageServingService.GenerateResponsiveImageSet(storagePath, options)

		imageData := map[string]interface{}{
			"id":            imageID,
			"alt_text":      altText,
			"display_order": displayOrder,
			"is_primary":    isPrimary,
			"image_set":     imageSet,
		}

		images = append(images, imageData)

		// Set primary image
		if isPrimary {
			primaryImage = imageData
		}
	}

	// If no primary image found, use the first one
	if primaryImage == nil && len(images) > 0 {
		primaryImage = images[0]
	}

	// Set cache headers
	c.Header("Cache-Control", "public, max-age=3600") // 1 hour for image sets
	c.Header("Vary", "Accept")

	c.JSON(http.StatusOK, gin.H{
		"product_id":    productID,
		"primary_image": primaryImage,
		"images":        images,
		"total_images":  len(images),
		"options":       options,
	})
}

// GetImagePlaceholder generates placeholder images for lazy loading
// @Summary Get image placeholder
// @Description Generate placeholder image for lazy loading
// @Tags image-serving
// @Accept json
// @Produce json
// @Param width query int false "Placeholder width" default(300)
// @Param height query int false "Placeholder height" default(300)
// @Param color query string false "Placeholder color (hex)" default(f3f4f6)
// @Success 200 {object} map[string]interface{}
// @Router /api/v1/images/placeholder [get]
func (h *ImageServingHandler) GetImagePlaceholder(c *gin.Context) {
	// Parse dimensions
	width := 300
	height := 300

	if w := c.Query("width"); w != "" {
		if parsed, err := strconv.Atoi(w); err == nil && parsed > 0 && parsed <= 2000 {
			width = parsed
		}
	}

	if h := c.Query("height"); h != "" {
		if parsed, err := strconv.Atoi(h); err == nil && parsed > 0 && parsed <= 2000 {
			height = parsed
		}
	}

	// Generate placeholder URL
	placeholderURL := h.imageServingService.GeneratePlaceholderURL(width, height)

	// Set cache headers for placeholders
	c.Header("Cache-Control", "public, max-age=31536000") // 1 year for placeholders
	c.Header("Content-Type", "application/json")

	c.JSON(http.StatusOK, gin.H{
		"placeholder_url": placeholderURL,
		"width":           width,
		"height":          height,
		"type":            "svg_placeholder",
	})
}

// ServeOptimizedImage serves an optimized image directly with proper headers
// @Summary Serve optimized image
// @Description Serve an optimized image file with CDN headers
// @Tags image-serving
// @Param id path string true "Product ID"
// @Param image_id path string true "Image ID"
// @Param size query string false "Image size" Enums(thumbnail, small, medium, large, original) default(medium)
// @Param format query string false "Image format" Enums(webp, jpeg, png, avif)
// @Success 200 {file} binary "Image file"
// @Failure 404 {object} middleware.ErrorResponse
// @Router /api/v1/images/{id}/{image_id}/serve [get]
func (h *ImageServingHandler) ServeOptimizedImage(c *gin.Context) {
	productIDStr := c.Param("id")
	productID, err := uuid.Parse(productIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid product ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	imageIDStr := c.Param("image_id")
	imageID, err := uuid.Parse(imageIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid image ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	// Get image record
	var storagePath string
	err = h.db.Postgres.QueryRow(`
		SELECT storage_path 
		FROM product_images 
		WHERE id = $1 AND product_id = $2
	`, imageID, productID).Scan(&storagePath)

	if err != nil {
		c.JSON(http.StatusNotFound, middleware.ErrorResponse{
			Error:   "not_found",
			Message: "Image not found",
			Code:    http.StatusNotFound,
		})
		return
	}

	// Parse parameters
	size := services.ImageSize(c.DefaultQuery("size", "medium"))
	format := services.ImageFormat(c.Query("format"))

	if format == "" {
		format = h.imageServingService.GetOptimalFormat(c.GetHeader("Accept"))
	}

	options := services.ImageServingOptions{
		Size:   size,
		Format: format,
	}

	// Generate optimized URL
	optimizedURL := h.imageServingService.GenerateOptimizedURL(storagePath, options)

	// Set CDN headers
	for key, value := range optimizedURL.Headers {
		c.Header(key, value)
	}

	// Redirect to the optimized image URL
	c.Redirect(http.StatusFound, optimizedURL.URL)
}
