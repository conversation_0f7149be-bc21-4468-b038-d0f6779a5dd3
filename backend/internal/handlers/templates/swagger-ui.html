<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}} - API Documentation</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5.17.14/swagger-ui.css" />
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }
        *, *:before, *:after {
            box-sizing: inherit;
        }
        body {
            margin: 0;
            background: #fafafa;
        }
        .swagger-ui .topbar {
            background-color: #1f2937;
            padding: 10px 20px;
        }
        .swagger-ui .topbar .download-url-wrapper .select-label {
            color: #ffffff;
        }
        .swagger-ui .topbar .download-url-wrapper input[type=text] {
            border: 2px solid #374151;
        }
        .custom-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
            margin-bottom: 0;
            position: relative;
            overflow: hidden;
        }
        .custom-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }
        .custom-header h1 {
            margin: 0;
            font-size: 2.8em;
            font-weight: 300;
            position: relative;
            z-index: 1;
        }
        .custom-header p {
            margin: 15px 0 0 0;
            font-size: 1.3em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        .api-info {
            background: white;
            padding: 25px;
            margin: 0;
            border-bottom: 1px solid #e5e7eb;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .api-info h2 {
            margin-top: 0;
            color: #1f2937;
            font-size: 1.8em;
        }
        .auth-notice {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 1px solid #f59e0b;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            position: relative;
            overflow: hidden;
        }
        .auth-notice::before {
            content: '🔐';
            position: absolute;
            top: -10px;
            right: -10px;
            font-size: 4em;
            opacity: 0.1;
        }
        .auth-notice h3 {
            margin-top: 0;
            color: #92400e;
            font-size: 1.2em;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .auth-notice p {
            margin-bottom: 15px;
            color: #92400e;
            line-height: 1.5;
        }
        .auth-actions {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            align-items: center;
        }
        .auth-btn {
            background: #3b82f6;
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            font-size: 14px;
            transition: all 0.2s;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            border: none;
            cursor: pointer;
        }
        .auth-btn:hover {
            background: #2563eb;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        .auth-btn.secondary {
            background: #6b7280;
            color: white;
        }
        .auth-btn.secondary:hover {
            background: #4b5563;
        }
        .auth-status {
            background: #f3f4f6;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            color: #374151;
            border: 1px solid #d1d5db;
        }
        .auth-status.authenticated {
            background: #d1fae5;
            color: #065f46;
            border-color: #10b981;
        }
        .auth-status.unauthenticated {
            background: #fee2e2;
            color: #991b1b;
            border-color: #ef4444;
        }
        .api-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .stat-item {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 20px 15px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid #e2e8f0;
            transition: all 0.2s;
        }
        .stat-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 0.9em;
            color: #64748b;
            font-weight: 500;
        }
        .quick-links {
            background: #f8fafc;
            padding: 25px;
            border-bottom: 1px solid #e5e7eb;
        }
        .quick-links h3 {
            margin: 0 0 15px 0;
            color: #1f2937;
            font-size: 1.3em;
        }
        .links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 15px;
        }
        .link-item {
            background: white;
            padding: 15px;
            border-radius: 10px;
            border: 1px solid #e5e7eb;
            text-decoration: none;
            color: #374151;
            transition: all 0.3s;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        .link-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(103, 126, 234, 0.1), transparent);
            transition: left 0.5s;
        }
        .link-item:hover::before {
            left: 100%;
        }
        .link-item:hover {
            border-color: #667eea;
            background: #f8faff;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(103, 126, 234, 0.15);
        }
        .link-title {
            font-weight: 600;
            color: #667eea;
            font-size: 1.1em;
            margin-bottom: 5px;
        }
        .link-desc {
            font-size: 0.9em;
            color: #6b7280;
            line-height: 1.4;
        }
        .footer-info {
            background: #1f2937;
            color: #9ca3af;
            padding: 20px;
            text-align: center;
            font-size: 0.9em;
        }
        .footer-info a {
            color: #60a5fa;
            text-decoration: none;
        }
        .footer-info a:hover {
            color: #93c5fd;
        }
    </style>
</head>
<body>
    <div class="custom-header">
        <h1>🏺 {{.Title}}</h1>
        <p>{{.Description}} - Version {{.Version}}</p>
    </div>

    <div class="api-info">
        <h2>🏺 API Overview</h2>
        <p>A comprehensive REST API for managing jewelry products, collections, orders, customers, and inventory. Built with Go, PostgreSQL, Redis, and MinIO for a modern e-commerce experience.</p>

        <div style="background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 15px; margin: 15px 0;">
            <h4 style="margin-top: 0; color: #0c4a6e; font-size: 1.1em;">📋 Key Features</h4>
            <ul style="margin: 0; padding-left: 20px; color: #0c4a6e;">
                <li><strong>Generic Image Upload:</strong> <code>/upload/image</code> - Upload images with automatic variant generation</li>
                <li><strong>Product Images:</strong> <code>/products/{id}/images</code> - Product-specific image management</li>
                <li><strong>Google OAuth:</strong> Secure authentication with email whitelist</li>
                <li><strong>Real-time Inventory:</strong> Stock tracking with low-stock alerts</li>
                <li><strong>Collection Sharing:</strong> Shareable collection URLs for customers</li>
            </ul>
        </div>

        <div class="auth-notice">
            <h3>🔐 Authentication Required</h3>
            <p>Most admin endpoints require Google OAuth authentication. Only whitelisted email addresses can access admin functions. Customer-facing endpoints (viewing collections, placing orders) remain public.</p>
            <div class="auth-actions">
                <a href="/api/v1/auth/google/login" class="auth-btn">
                    🚀 Login with Google
                </a>
                <button id="logout-btn" class="auth-btn secondary" style="display: none;">
                    🚪 Logout
                </button>
                <span id="auth-status" class="auth-status unauthenticated">
                    ❌ Not Authenticated
                </span>
            </div>
        </div>
        
        <div class="api-stats">
            <div class="stat-item">
                <div class="stat-number">35+</div>
                <div class="stat-label">API Endpoints</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">8</div>
                <div class="stat-label">Core Modules</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">12</div>
                <div class="stat-label">Database Tables</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">🔐</div>
                <div class="stat-label">OAuth Secured</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">100%</div>
                <div class="stat-label">OpenAPI 3.0</div>
            </div>
        </div>
    </div>

    <div class="quick-links">
        <h3>🚀 Quick Access</h3>
        <div class="links-grid">
            <div class="link-item" onclick="scrollToTag('auth')">
                <div class="link-title">🔐 Authentication</div>
                <div class="link-desc">Google OAuth login & JWT tokens</div>
            </div>
            <div class="link-item" onclick="scrollToTag('Products')">
                <div class="link-title">📦 Products</div>
                <div class="link-desc">Manage jewelry items with images</div>
            </div>
            <div class="link-item" onclick="scrollToTag('Collections')">
                <div class="link-title">📚 Collections</div>
                <div class="link-desc">Curated product groups & sharing</div>
            </div>
            <div class="link-item" onclick="scrollToTag('Orders')">
                <div class="link-title">🛒 Orders</div>
                <div class="link-desc">Order processing & management</div>
            </div>
            <div class="link-item" onclick="scrollToTag('Customers')">
                <div class="link-title">👥 Customers</div>
                <div class="link-desc">Customer information & statistics</div>
            </div>
            <div class="link-item" onclick="scrollToTag('Inventory')">
                <div class="link-title">📊 Inventory</div>
                <div class="link-desc">Stock tracking & alerts</div>
            </div>
            <div class="link-item" onclick="scrollToTag('Upload')">
                <div class="link-title">🖼️ Upload</div>
                <div class="link-desc">Generic image upload & product images</div>
            </div>
            <div class="link-item" onclick="scrollToTag('Activities')">
                <div class="link-title">📋 Activities</div>
                <div class="link-desc">System activity logs & tracking</div>
            </div>
        </div>
    </div>

    <div id="swagger-ui"></div>

    <!-- Footer -->
    <div class="footer-info">
        <p>© 2024 Anand Jewels. All rights reserved. |
        <a href="https://github.com/manojm321/anandjewels" target="_blank">GitHub</a> |
        <a href="/health">Health Check</a> |
        Built with ❤️ using Go, PostgreSQL, Redis & MinIO</p>
    </div>

    <script src="https://unpkg.com/swagger-ui-dist@5.17.14/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@5.17.14/swagger-ui-standalone-preset.js"></script>
    <script>
        window.onload = function() {
            const ui = SwaggerUIBundle({
                url: '/api/docs/spec.yaml',
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                defaultModelsExpandDepth: 1,
                defaultModelExpandDepth: 1,
                docExpansion: "list",
                filter: true,
                showExtensions: true,
                showCommonExtensions: true,
                tryItOutEnabled: true,
                validatorUrl: null,
                // Enable authentication
                persistAuthorization: true,
                // Add request interceptor to include auth token
                requestInterceptor: function(request) {
                    const token = localStorage.getItem('jwt_token');
                    if (token) {
                        request.headers['Authorization'] = 'Bearer ' + token;
                    }
                    return request;
                },
                // Add response interceptor to handle auth errors
                responseInterceptor: function(response) {
                    if (response.status === 401) {
                        localStorage.removeItem('jwt_token');
                        updateAuthStatus();
                        showNotification('Authentication expired. Please login again.', 'error');
                    }
                    return response;
                }
            });

            window.ui = ui;

            // Initialize authentication status
            setTimeout(function() {
                initializeAuth();
            }, 1000);
        };

        function scrollToTag(tagName) {
            setTimeout(() => {
                const element = document.querySelector(`[data-tag="${tagName}"]`);
                if (element) {
                    element.scrollIntoView({ behavior: 'smooth' });
                } else {
                    // Fallback: try to find by text content
                    const elements = document.querySelectorAll('.opblock-tag-section h3');
                    for (let el of elements) {
                        if (el.textContent.includes(tagName)) {
                            el.scrollIntoView({ behavior: 'smooth' });
                            break;
                        }
                    }
                }
            }, 1000); // Wait for Swagger UI to load
        }

        function initializeAuth() {
            // Check for auth token in URL (from OAuth callback)
            const urlParams = new URLSearchParams(window.location.search);
            const token = urlParams.get('token');

            if (token) {
                localStorage.setItem('jwt_token', token);
                // Clean up URL
                window.history.replaceState({}, document.title, window.location.pathname);
                showNotification('Successfully authenticated!', 'success');
            }

            updateAuthStatus();
            setupAuthHandlers();
        }

        function updateAuthStatus() {
            const token = localStorage.getItem('jwt_token');
            const authStatus = document.getElementById('auth-status');
            const logoutBtn = document.getElementById('logout-btn');

            if (token) {
                authStatus.textContent = '✅ Authenticated';
                authStatus.className = 'auth-status authenticated';
                logoutBtn.style.display = 'inline-flex';

                // Verify token validity
                verifyToken(token);
            } else {
                authStatus.textContent = '❌ Not Authenticated';
                authStatus.className = 'auth-status unauthenticated';
                logoutBtn.style.display = 'none';
            }
        }

        function setupAuthHandlers() {
            const logoutBtn = document.getElementById('logout-btn');
            if (logoutBtn) {
                logoutBtn.onclick = function() {
                    localStorage.removeItem('jwt_token');
                    updateAuthStatus();
                    showNotification('Logged out successfully', 'info');
                };
            }
        }

        async function verifyToken(token) {
            try {
                const response = await fetch('/api/v1/auth/me', {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });

                if (!response.ok) {
                    throw new Error('Token verification failed');
                }

                const data = await response.json();
                if (data.user) {
                    const authStatus = document.getElementById('auth-status');
                    authStatus.textContent = `✅ ${data.user.name} (${data.user.role})`;
                }
            } catch (error) {
                localStorage.removeItem('jwt_token');
                updateAuthStatus();
                showNotification('Authentication expired', 'warning');
            }
        }

        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                animation: slideIn 0.3s ease-out;
            `;

            switch (type) {
                case 'success':
                    notification.style.background = '#10b981';
                    break;
                case 'error':
                    notification.style.background = '#ef4444';
                    break;
                case 'warning':
                    notification.style.background = '#f59e0b';
                    break;
                default:
                    notification.style.background = '#3b82f6';
            }

            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 4000);
        }

        // Add CSS animation for notifications
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
