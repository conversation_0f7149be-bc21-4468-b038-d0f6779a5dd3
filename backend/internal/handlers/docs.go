package handlers

import (
	"embed"
	"html/template"
	"net/http"

	"github.com/gin-gonic/gin"
)

//go:embed templates/*
var docsTemplates embed.FS

//go:embed static/*
var docsStatic embed.FS

// DocsHandler handles API documentation endpoints
type DocsHandler struct{}

// NewDocsHandler creates a new documentation handler
func NewDocsHandler() *DocsHandler {
	return &DocsHandler{}
}

// ServeSwaggerUI serves the Swagger UI documentation
func (h *DocsHandler) ServeSwaggerUI(c *gin.Context) {
	tmpl, err := template.ParseFS(docsTemplates, "templates/swagger-ui.html")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "template_error",
			"message": "Failed to load documentation template",
		})
		return
	}

	data := struct {
		Title       string
		Description string
		Version     string
		BaseURL     string
	}{
		Title:       "Jewelry E-Commerce Platform API",
		Description: "Comprehensive REST API Documentation",
		Version:     "1.0.0",
		BaseURL:     "/api/v1",
	}

	c<PERSON><PERSON>("Content-Type", "text/html; charset=utf-8")
	err = tmpl.Execute(c.Writer, data)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "render_error",
			"message": "Failed to render documentation",
		})
		return
	}
}

// ServeOpenAPISpec serves the OpenAPI specification in YAML format
func (h *DocsHandler) ServeOpenAPISpec(c *gin.Context) {
	specContent, err := docsStatic.ReadFile("static/api-spec.yaml")
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "spec_not_found",
			"message": "OpenAPI specification not found",
		})
		return
	}

	c.Header("Content-Type", "application/x-yaml")
	c.Header("Content-Disposition", "inline; filename=api-spec.yaml")
	c.Data(http.StatusOK, "application/x-yaml", specContent)
}

// ServeOpenAPIJSON serves the OpenAPI specification in JSON format
func (h *DocsHandler) ServeOpenAPIJSON(c *gin.Context) {
	// For now, we'll redirect to YAML. In the future, we could convert YAML to JSON
	c.Redirect(http.StatusFound, "/api/docs/spec.yaml")
}

// RedirectToDocs redirects /api/docs to /api/docs/
func (h *DocsHandler) RedirectToDocs(c *gin.Context) {
	c.Redirect(http.StatusMovedPermanently, "/api/docs/")
}
