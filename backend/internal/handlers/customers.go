package handlers

import (
	"database/sql"
	"net/http"
	"strconv"

	"github.com/anandjewels/jewelry-backend/internal/database"
	"github.com/anandjewels/jewelry-backend/internal/middleware"
	"github.com/anandjewels/jewelry-backend/internal/models"
	"github.com/anandjewels/jewelry-backend/internal/services"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// CustomerHandler handles customer-related HTTP requests
type CustomerHandler struct {
	db              *database.DB
	customerService *services.CustomerService
}

// NewCustomerHandler creates a new customer handler
func NewCustomerHandler(db *database.DB) *CustomerHandler {
	return &CustomerHandler{
		db:              db,
		customerService: services.NewCustomerService(db),
	}
}

// CreateCustomer creates a new customer
// @Summary Create a new customer
// @Description Create a new customer
// @Tags customers
// @Accept json
// @Produce json
// @Param customer body models.CreateCustomerRequest true "Customer data"
// @Success 201 {object} models.CustomerResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/customers [post]
func (h *CustomerHandler) CreateCustomer(c *gin.Context) {
	var req models.CreateCustomerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_request",
			Message: "Invalid request body",
			Code:    http.StatusBadRequest,
			Details: err.Error(),
		})
		return
	}

	customer, err := h.customerService.CreateCustomer(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "creation_failed",
			Message: "Failed to create customer",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, customer.ToResponse())
}

// GetCustomers retrieves a list of customers with filtering and pagination
// @Summary Get customers
// @Description Get a list of customers with optional filtering and pagination
// @Tags customers
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param search query string false "Search in name, email, and phone"
// @Param is_active query bool false "Filter by active status"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/customers [get]
func (h *CustomerHandler) GetCustomers(c *gin.Context) {
	// Parse query parameters
	req := &models.CustomerListRequest{
		Page:  1,
		Limit: 20,
	}

	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			req.Page = p
		}
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil && l > 0 && l <= 100 {
			req.Limit = l
		}
	}

	if search := c.Query("search"); search != "" {
		req.Search = &search
	}

	if isActive := c.Query("is_active"); isActive != "" {
		if active, err := strconv.ParseBool(isActive); err == nil {
			req.IsActive = &active
		}
	}

	if sortBy := c.Query("sort_by"); sortBy != "" {
		req.SortBy = sortBy
	}

	if sortOrder := c.Query("sort_order"); sortOrder != "" {
		req.SortOrder = sortOrder
	}

	customers, total, err := h.customerService.GetCustomers(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "fetch_failed",
			Message: "Failed to fetch customers",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	// Convert to response format
	customerResponses := make([]*models.CustomerResponse, len(customers))
	for i, customer := range customers {
		customerResponses[i] = customer.ToResponse()
	}

	c.Header("X-Total-Count", strconv.Itoa(total))
	c.JSON(http.StatusOK, gin.H{
		"customers": customerResponses,
		"total":     total,
		"page":      req.Page,
		"limit":     req.Limit,
	})
}

// GetCustomer retrieves a single customer by ID
// @Summary Get customer by ID
// @Description Get a single customer by its ID
// @Tags customers
// @Accept json
// @Produce json
// @Param id path string true "Customer ID"
// @Success 200 {object} models.CustomerResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/customers/{id} [get]
func (h *CustomerHandler) GetCustomer(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid customer ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	customer, err := h.customerService.GetCustomerByID(id)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error:   "not_found",
				Message: "Customer not found",
				Code:    http.StatusNotFound,
			})
			return
		}
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "fetch_failed",
			Message: "Failed to fetch customer",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, customer.ToResponse())
}

// UpdateCustomer updates a customer
// @Summary Update customer
// @Description Update a customer by ID
// @Tags customers
// @Accept json
// @Produce json
// @Param id path string true "Customer ID"
// @Param customer body models.UpdateCustomerRequest true "Customer update data"
// @Success 200 {object} models.CustomerResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 404 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/customers/{id} [put]
func (h *CustomerHandler) UpdateCustomer(c *gin.Context) {
	idStr := c.Param("id")
	id, err := uuid.Parse(idStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_id",
			Message: "Invalid customer ID format",
			Code:    http.StatusBadRequest,
		})
		return
	}

	var req models.UpdateCustomerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_request",
			Message: "Invalid request body",
			Code:    http.StatusBadRequest,
			Details: err.Error(),
		})
		return
	}

	customer, err := h.customerService.UpdateCustomer(id, &req)
	if err != nil {
		if err == sql.ErrNoRows {
			c.JSON(http.StatusNotFound, middleware.ErrorResponse{
				Error:   "not_found",
				Message: "Customer not found",
				Code:    http.StatusNotFound,
			})
			return
		}
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "update_failed",
			Message: "Failed to update customer",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, customer.ToResponse())
}
