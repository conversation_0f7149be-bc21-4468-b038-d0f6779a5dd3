package handlers

import (
	"net/http"
	"strconv"

	"github.com/anandjewels/jewelry-backend/internal/database"
	"github.com/anandjewels/jewelry-backend/internal/middleware"
	"github.com/anandjewels/jewelry-backend/internal/models"
	"github.com/anandjewels/jewelry-backend/internal/services"
	"github.com/gin-gonic/gin"
)

// ActivityHandler handles activity-related HTTP requests
type ActivityHandler struct {
	db              *database.DB
	activityService *services.ActivityService
}

// NewActivityHandler creates a new activity handler
func NewActivityHandler(db *database.DB) *ActivityHandler {
	return &ActivityHandler{
		db:              db,
		activityService: services.NewActivityService(db),
	}
}

// GetActivities retrieves a list of activities with filtering and pagination
// @Summary Get activities
// @Description Get a list of activities with optional filtering and pagination
// @Tags activities
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(20)
// @Param type query string false "Activity type filter"
// @Param entity_type query string false "Entity type filter"
// @Param start_date query string false "Start date filter (ISO 8601)"
// @Param end_date query string false "End date filter (ISO 8601)"
// @Success 200 {object} models.ActivityListResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/activities [get]
func (h *ActivityHandler) GetActivities(c *gin.Context) {
	var req models.ActivityListRequest

	// Parse query parameters
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			req.Page = page
		}
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 && limit <= 100 {
			req.Limit = limit
		}
	}

	if activityType := c.Query("type"); activityType != "" {
		req.Type = models.ActivityType(activityType)
	}

	if entityType := c.Query("entity_type"); entityType != "" {
		req.EntityType = entityType
	}

	if startDate := c.Query("start_date"); startDate != "" {
		req.StartDate = startDate
	}

	if endDate := c.Query("end_date"); endDate != "" {
		req.EndDate = endDate
	}

	// Set defaults
	if req.Page == 0 {
		req.Page = 1
	}
	if req.Limit == 0 {
		req.Limit = 20
	}

	activities, total, err := h.activityService.GetActivities(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "fetch_failed",
			Message: "Failed to fetch activities",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	// Convert to response format
	activityResponses := make([]*models.ActivityResponse, len(activities))
	for i, activity := range activities {
		activityResponses[i] = activity.ToResponse()
	}

	totalPages := (total + req.Limit - 1) / req.Limit

	response := &models.ActivityListResponse{
		Activities: activityResponses,
		Total:      total,
		Page:       req.Page,
		Limit:      req.Limit,
		TotalPages: totalPages,
	}

	c.JSON(http.StatusOK, response)
}

// CreateActivity creates a new activity
// @Summary Create activity
// @Description Create a new activity record
// @Tags activities
// @Accept json
// @Produce json
// @Param activity body models.CreateActivityRequest true "Activity data"
// @Success 201 {object} models.ActivityResponse
// @Failure 400 {object} middleware.ErrorResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/activities [post]
func (h *ActivityHandler) CreateActivity(c *gin.Context) {
	var req models.CreateActivityRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, middleware.ErrorResponse{
			Error:   "invalid_request",
			Message: "Invalid request body",
			Code:    http.StatusBadRequest,
			Details: err.Error(),
		})
		return
	}

	activity, err := h.activityService.CreateActivity(&req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "creation_failed",
			Message: "Failed to create activity",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, activity.ToResponse())
}

// GetRecentActivities gets the most recent activities
// @Summary Get recent activities
// @Description Get the most recent activities (convenience endpoint)
// @Tags activities
// @Accept json
// @Produce json
// @Param limit query int false "Number of activities to return" default(10)
// @Success 200 {array} models.ActivityResponse
// @Failure 500 {object} middleware.ErrorResponse
// @Router /api/v1/activities/recent [get]
func (h *ActivityHandler) GetRecentActivities(c *gin.Context) {
	limit := 10
	if limitStr := c.Query("limit"); limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 && parsedLimit <= 50 {
			limit = parsedLimit
		}
	}

	activities, err := h.activityService.GetRecentActivities(limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, middleware.ErrorResponse{
			Error:   "fetch_failed",
			Message: "Failed to fetch recent activities",
			Code:    http.StatusInternalServerError,
			Details: err.Error(),
		})
		return
	}

	// Convert to response format
	activityResponses := make([]*models.ActivityResponse, len(activities))
	for i, activity := range activities {
		activityResponses[i] = activity.ToResponse()
	}

	c.JSON(http.StatusOK, activityResponses)
}
