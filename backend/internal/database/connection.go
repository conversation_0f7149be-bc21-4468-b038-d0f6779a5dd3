package database

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"os"
	"time"

	_ "github.com/lib/pq" // PostgreSQL driver
	"github.com/redis/go-redis/v9"
)

// DB holds the database connections
type DB struct {
	Postgres *sql.DB
	Redis    *redis.Client
}

// Config holds database configuration
type Config struct {
	PostgresHost     string
	PostgresPort     string
	PostgresDB       string
	PostgresUser     string
	PostgresPassword string
	PostgresSSLMode  string
	RedisURL         string
	RedisPassword    string
}

// LoadConfig loads database configuration from environment variables
func LoadConfig() *Config {
	return &Config{
		PostgresHost:     getEnv("DB_HOST", "localhost"),
		PostgresPort:     getEnv("DB_PORT", "5432"),
		PostgresDB:       getEnv("DB_NAME", "jewelry_db"),
		PostgresUser:     getEnv("DB_USER", "postgres"),
		PostgresPassword: getEnv("DB_PASSWORD", "password"),
		PostgresSSLMode:  getEnv("DB_SSL_MODE", "disable"),
		RedisURL:         getEnv("REDIS_URL", "redis://localhost:6379"),
		RedisPassword:    getEnv("REDIS_PASSWORD", ""),
	}
}

// Connect establishes connections to PostgreSQL and Redis
func Connect(config *Config) (*DB, error) {
	// Connect to PostgreSQL
	postgresDB, err := connectPostgres(config)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to PostgreSQL: %w", err)
	}

	// Connect to Redis
	redisClient, err := connectRedis(config)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	return &DB{
		Postgres: postgresDB,
		Redis:    redisClient,
	}, nil
}

// connectPostgres establishes PostgreSQL connection
func connectPostgres(config *Config) (*sql.DB, error) {
	dsn := fmt.Sprintf(
		"host=%s port=%s user=%s password=%s dbname=%s sslmode=%s",
		config.PostgresHost,
		config.PostgresPort,
		config.PostgresUser,
		config.PostgresPassword,
		config.PostgresDB,
		config.PostgresSSLMode,
	)

	db, err := sql.Open("postgres", dsn)
	if err != nil {
		return nil, err
	}

	// Configure connection pool
	db.SetMaxOpenConns(25)
	db.SetMaxIdleConns(5)
	db.SetConnMaxLifetime(5 * time.Minute)

	// Test the connection
	if err := db.Ping(); err != nil {
		return nil, err
	}

	log.Printf("✅ Connected to PostgreSQL at %s:%s", config.PostgresHost, config.PostgresPort)
	return db, nil
}

// connectRedis establishes Redis connection
func connectRedis(config *Config) (*redis.Client, error) {
	opt, err := redis.ParseURL(config.RedisURL)
	if err != nil {
		return nil, err
	}

	if config.RedisPassword != "" {
		opt.Password = config.RedisPassword
	}

	client := redis.NewClient(opt)

	// Test the connection
	ctx := context.Background()
	if err := client.Ping(ctx).Err(); err != nil {
		return nil, err
	}

	log.Printf("✅ Connected to Redis at %s", config.RedisURL)
	return client, nil
}

// Close closes all database connections
func (db *DB) Close() error {
	var err error

	if db.Postgres != nil {
		if closeErr := db.Postgres.Close(); closeErr != nil {
			err = closeErr
		}
	}

	if db.Redis != nil {
		if closeErr := db.Redis.Close(); closeErr != nil {
			err = closeErr
		}
	}

	return err
}

// getEnv gets environment variable with fallback
func getEnv(key, fallback string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return fallback
}
