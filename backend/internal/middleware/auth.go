package middleware

import (
	"net/http"
	"strings"

	"github.com/anandjewels/jewelry-backend/internal/services"
	"github.com/gin-gonic/gin"
)

// AuthMiddleware validates JWT tokens and sets user context
func AuthMiddleware(authService *services.AuthService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from Authorization header
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "missing_token",
				Message: "Authorization header is required",
				Code:    http.StatusUnauthorized,
			})
			c.Abort()
			return
		}

		// Check if header starts with "Bearer "
		if !strings.HasPrefix(authHeader, "Bearer ") {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "invalid_token_format",
				Message: "Authorization header must start with 'Bearer '",
				Code:    http.StatusUnauthorized,
			})
			c.Abort()
			return
		}

		// Extract token
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "empty_token",
				Message: "JWT token is required",
				Code:    http.StatusUnauthorized,
			})
			c.Abort()
			return
		}

		// Validate token
		claims, err := authService.ValidateJWT(token)
		if err != nil {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "invalid_token",
				Message: "Invalid or expired JWT token",
				Code:    http.StatusUnauthorized,
				Details: err.Error(),
			})
			c.Abort()
			return
		}

		// Check if user is active
		if !claims.IsActive {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "user_inactive",
				Message: "User account is inactive",
				Code:    http.StatusUnauthorized,
			})
			c.Abort()
			return
		}

		// Set user in context
		c.Set("user", claims)
		c.Set("user_id", claims.UserID)
		c.Set("user_email", claims.Email)
		c.Set("user_role", claims.Role)

		c.Next()
	}
}

// OptionalAuthMiddleware validates JWT tokens if present but doesn't require them
func OptionalAuthMiddleware(authService *services.AuthService) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			// No token provided, continue without authentication
			c.Next()
			return
		}

		// Check if header starts with "Bearer "
		if !strings.HasPrefix(authHeader, "Bearer ") {
			// Invalid format, continue without authentication
			c.Next()
			return
		}

		// Extract token
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			// Empty token, continue without authentication
			c.Next()
			return
		}

		// Validate token
		claims, err := authService.ValidateJWT(token)
		if err != nil {
			// Invalid token, continue without authentication
			c.Next()
			return
		}

		// Check if user is active
		if !claims.IsActive {
			// Inactive user, continue without authentication
			c.Next()
			return
		}

		// Set user in context if token is valid
		c.Set("user", claims)
		c.Set("user_id", claims.UserID)
		c.Set("user_email", claims.Email)
		c.Set("user_role", claims.Role)

		c.Next()
	}
}

// AdminOnlyMiddleware ensures only admin users can access the endpoint
func AdminOnlyMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user from context (should be set by AuthMiddleware)
		userInterface, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "authentication_required",
				Message: "Authentication is required for this endpoint",
				Code:    http.StatusUnauthorized,
			})
			c.Abort()
			return
		}

		claims, ok := userInterface.(*services.JWTClaims)
		if !ok {
			c.JSON(http.StatusUnauthorized, ErrorResponse{
				Error:   "invalid_user_context",
				Message: "Invalid user information in request context",
				Code:    http.StatusUnauthorized,
			})
			c.Abort()
			return
		}

		// Check if user has admin role
		if claims.Role != "admin" && claims.Role != "super_admin" {
			c.JSON(http.StatusForbidden, ErrorResponse{
				Error:   "insufficient_permissions",
				Message: "Admin access is required for this endpoint",
				Code:    http.StatusForbidden,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RateLimitMiddleware implements basic rate limiting for authentication endpoints
func RateLimitMiddleware() gin.HandlerFunc {
	// This is a simple implementation. In production, you might want to use Redis
	// or a more sophisticated rate limiting library
	return func(c *gin.Context) {
		// For now, just continue. In production, implement proper rate limiting
		// based on IP address, user ID, or other criteria
		c.Next()
	}
}
