package middleware

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

// ImageCacheConfig holds configuration for image caching
type ImageCacheConfig struct {
	MaxAge          int    // Cache duration in seconds
	EnableGzip      bool   // Enable gzip compression
	EnableBrotli    bool   // Enable brotli compression
	EnableETag      bool   // Enable ETag headers
	EnableVary      bool   // Enable Vary headers
	StaleWhileRevalidate int // Stale-while-revalidate duration
}

// DefaultImageCacheConfig returns default image cache configuration
func DefaultImageCacheConfig() ImageCacheConfig {
	return ImageCacheConfig{
		MaxAge:               86400 * 7, // 7 days
		EnableGzip:           true,
		EnableBrotli:         true,
		EnableETag:           true,
		EnableVary:           true,
		StaleWhileRevalidate: 86400, // 1 day
	}
}

// ImageCacheMiddleware adds appropriate caching headers for image responses
func ImageCacheMiddleware(config ...ImageCacheConfig) gin.HandlerFunc {
	cfg := DefaultImageCacheConfig()
	if len(config) > 0 {
		cfg = config[0]
	}

	return func(c *gin.Context) {
		// Only apply to image-related endpoints
		if !isImageEndpoint(c.Request.URL.Path) {
			c.Next()
			return
		}

		// Set cache headers before processing
		setCacheHeaders(c, cfg)

		// Check for conditional requests
		if handleConditionalRequest(c) {
			return
		}

		c.Next()

		// Add additional headers after processing
		addPostProcessHeaders(c, cfg)
	}
}

// isImageEndpoint checks if the endpoint is image-related
func isImageEndpoint(path string) bool {
	imageEndpoints := []string{
		"/api/v1/images/",
		"/api/v1/products/",
	}

	for _, endpoint := range imageEndpoints {
		if strings.Contains(path, endpoint) {
			return true
		}
	}

	return false
}

// setCacheHeaders sets basic cache control headers
func setCacheHeaders(c *gin.Context, cfg ImageCacheConfig) {
	// Basic cache control
	cacheControl := fmt.Sprintf("public, max-age=%d", cfg.MaxAge)
	
	if cfg.StaleWhileRevalidate > 0 {
		cacheControl += fmt.Sprintf(", stale-while-revalidate=%d", cfg.StaleWhileRevalidate)
	}

	c.Header("Cache-Control", cacheControl)
	c.Header("Expires", time.Now().Add(time.Duration(cfg.MaxAge)*time.Second).Format(time.RFC1123))

	// CORS headers for image serving
	c.Header("Access-Control-Allow-Origin", "*")
	c.Header("Access-Control-Allow-Methods", "GET, HEAD, OPTIONS")
	c.Header("Access-Control-Allow-Headers", "Accept, Accept-Encoding, If-None-Match, If-Modified-Since")

	// Security headers
	c.Header("X-Content-Type-Options", "nosniff")
	c.Header("X-Frame-Options", "SAMEORIGIN")

	// Vary header for content negotiation
	if cfg.EnableVary {
		c.Header("Vary", "Accept, Accept-Encoding")
	}
}

// handleConditionalRequest handles If-None-Match and If-Modified-Since headers
func handleConditionalRequest(c *gin.Context) bool {
	// Handle If-None-Match (ETag)
	if ifNoneMatch := c.GetHeader("If-None-Match"); ifNoneMatch != "" {
		// For now, we'll skip ETag validation since we don't have stored ETags
		// In a full implementation, you'd check against stored ETags
	}

	// Handle If-Modified-Since
	if ifModifiedSince := c.GetHeader("If-Modified-Since"); ifModifiedSince != "" {
		if parsedTime, err := time.Parse(time.RFC1123, ifModifiedSince); err == nil {
			// For images, we can assume they don't change often
			// If the request is for a resource older than 1 hour, return 304
			if time.Since(parsedTime) > time.Hour {
				c.Status(http.StatusNotModified)
				return true
			}
		}
	}

	return false
}

// addPostProcessHeaders adds headers after the response is processed
func addPostProcessHeaders(c *gin.Context, cfg ImageCacheConfig) {
	// Add ETag if enabled and not already set
	if cfg.EnableETag && c.GetHeader("ETag") == "" {
		// Generate simple ETag based on URL and current time (simplified)
		etag := fmt.Sprintf(`"%x"`, time.Now().Unix())
		c.Header("ETag", etag)
	}

	// Add Last-Modified if not set
	if c.GetHeader("Last-Modified") == "" {
		c.Header("Last-Modified", time.Now().Format(time.RFC1123))
	}

	// Add compression headers if applicable
	acceptEncoding := c.GetHeader("Accept-Encoding")
	if cfg.EnableBrotli && strings.Contains(acceptEncoding, "br") {
		c.Header("Content-Encoding", "br")
	} else if cfg.EnableGzip && strings.Contains(acceptEncoding, "gzip") {
		c.Header("Content-Encoding", "gzip")
	}
}

// ImageCompressionMiddleware handles image compression based on Accept-Encoding
func ImageCompressionMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Only apply to image endpoints
		if !isImageEndpoint(c.Request.URL.Path) {
			c.Next()
			return
		}

		acceptEncoding := c.GetHeader("Accept-Encoding")
		
		// Set appropriate compression headers
		if strings.Contains(acceptEncoding, "br") {
			c.Header("Accept-Encoding", "br, gzip, deflate")
		} else if strings.Contains(acceptEncoding, "gzip") {
			c.Header("Accept-Encoding", "gzip, deflate")
		}

		c.Next()
	}
}

// LazyLoadingMiddleware adds headers to support lazy loading
func LazyLoadingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Only apply to image endpoints
		if !isImageEndpoint(c.Request.URL.Path) {
			c.Next()
			return
		}

		// Add headers that help with lazy loading
		c.Header("X-Lazy-Loading", "supported")
		
		// Add Link header for preloading critical images
		if c.Query("priority") == "high" {
			c.Header("Link", fmt.Sprintf("<%s>; rel=preload; as=image", c.Request.URL.Path))
		}

		c.Next()
	}
}

// DeviceOptimizationMiddleware optimizes responses based on device type
func DeviceOptimizationMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Only apply to image endpoints
		if !isImageEndpoint(c.Request.URL.Path) {
			c.Next()
			return
		}

		// Detect device type from User-Agent or custom header
		userAgent := strings.ToLower(c.GetHeader("User-Agent"))
		deviceType := "desktop"

		if strings.Contains(userAgent, "mobile") || strings.Contains(userAgent, "android") || strings.Contains(userAgent, "iphone") {
			deviceType = "mobile"
		} else if strings.Contains(userAgent, "tablet") || strings.Contains(userAgent, "ipad") {
			deviceType = "tablet"
		}

		// Override with explicit device type if provided
		if dt := c.Query("device"); dt != "" {
			deviceType = dt
		}

		// Add device type to context for handlers to use
		c.Set("device_type", deviceType)

		// Add device-specific headers
		c.Header("X-Device-Type", deviceType)
		
		// Add viewport hints for mobile devices
		if deviceType == "mobile" {
			c.Header("X-Mobile-Optimized", "true")
		}

		c.Next()
	}
}

// QualityOptimizationMiddleware optimizes image quality based on connection
func QualityOptimizationMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Only apply to image endpoints
		if !isImageEndpoint(c.Request.URL.Path) {
			c.Next()
			return
		}

		// Check for Save-Data header (data saver mode)
		saveData := c.GetHeader("Save-Data")
		
		// Check for connection type hints
		networkInfo := c.GetHeader("Downlink") // Network Information API
		
		quality := 85 // Default quality
		
		if saveData == "on" {
			quality = 60 // Lower quality for data saver
		} else if networkInfo != "" {
			// Parse downlink speed and adjust quality
			if downlink, err := strconv.ParseFloat(networkInfo, 64); err == nil {
				if downlink < 1.0 { // Slow connection
					quality = 70
				} else if downlink > 10.0 { // Fast connection
					quality = 95
				}
			}
		}

		// Add quality to context
		c.Set("image_quality", quality)
		
		// Add headers
		c.Header("X-Image-Quality", strconv.Itoa(quality))
		if saveData == "on" {
			c.Header("X-Data-Saver", "active")
		}

		c.Next()
	}
}
