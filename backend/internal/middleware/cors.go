package middleware

import (
	"os"
	"strings"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

// CORS returns a CORS middleware configured for the application
func CORS() gin.HandlerFunc {
	// Get allowed origins from environment
	originsEnv := os.Getenv("CORS_ORIGINS")
	if originsEnv == "" {
		// Default origins for development - includes both React (3000) and Vite (5173) dev servers
		originsEnv = "http://localhost:3000,http://localhost:5173"
	}

	origins := strings.Split(originsEnv, ",")
	for i, origin := range origins {
		origins[i] = strings.TrimSpace(origin)
	}

	config := cors.Config{
		AllowOrigins:     origins,
		AllowMethods:     []string{"GET", "POST", "PUT", "PATCH", "DELETE", "HEAD", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Length", "Content-Type", "Authorization", "X-Requested-With"},
		ExposeHeaders:    []string{"Content-Length", "X-Total-Count"},
		AllowCredentials: true,
	}

	return cors.New(config)
}
