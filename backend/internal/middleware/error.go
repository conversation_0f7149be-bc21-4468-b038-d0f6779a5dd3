package middleware

import (
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
)

// ErrorResponse represents a standardized error response
type ErrorResponse struct {
	Error   string      `json:"error"`
	Message string      `json:"message"`
	Code    int         `json:"code"`
	Details interface{} `json:"details,omitempty"`
}

// ErrorHandler returns a middleware that handles panics and errors
func ErrorHandler() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		if err, ok := recovered.(string); ok {
			log.Printf("Panic recovered: %s", err)
			c.JSO<PERSON>(http.StatusInternalServerError, ErrorResponse{
				Error:   "internal_server_error",
				Message: "An internal server error occurred",
				Code:    http.StatusInternalServerError,
			})
		} else if err, ok := recovered.(error); ok {
			log.Printf("Panic recovered: %v", err)
			c.<PERSON>(http.StatusInternalServerError, ErrorResponse{
				Error:   "internal_server_error",
				Message: "An internal server error occurred",
				Code:    http.StatusInternalServerError,
			})
		} else {
			log.Printf("Panic recovered: %v", recovered)
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error:   "internal_server_error",
				Message: "An internal server error occurred",
				Code:    http.StatusInternalServerError,
			})
		}
		c.Abort()
	})
}

// NotFoundHandler returns a 404 handler
func NotFoundHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error:   "not_found",
			Message: "The requested resource was not found",
			Code:    http.StatusNotFound,
		})
	}
}

// MethodNotAllowedHandler returns a 405 handler
func MethodNotAllowedHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.JSON(http.StatusMethodNotAllowed, ErrorResponse{
			Error:   "method_not_allowed",
			Message: "The requested method is not allowed for this resource",
			Code:    http.StatusMethodNotAllowed,
		})
	}
}
