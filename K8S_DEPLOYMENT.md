# Kubernetes Deployment Guide

This guide explains how to deploy the Anand Jewels application to Kubernetes.

## Prerequisites

1. **Kubernetes cluster** (local or cloud)
2. **kubectl** configured to access your cluster
3. **Docker** for building images
4. **Container registry** (Docker Hub, AWS ECR, GCR, etc.)
5. **Ingress controller** (nginx-ingress recommended)
6. **cert-manager** (optional, for SSL certificates)

## Quick Start

### 1. Build and Push Images

```bash
# Build all Docker images
make k8s-build-images

# Push to your registry (replace with your registry)
REGISTRY=your-registry.com make k8s-push-images
```

### 2. Update Image References

Update the image references in the Kubernetes manifests:
- `k8s/backend.yaml`: Change `anand-jewels/backend:latest` to `your-registry.com/anand-jewels/backend:latest`
- `k8s/frontend.yaml`: Change `anand-jewels/frontend:latest` to `your-registry.com/anand-jewels/frontend:latest`
- `k8s/admin-portal.yaml`: Change `anand-jewels/admin-portal:latest` to `your-registry.com/anand-jewels/admin-portal:latest`

### 3. Configure Secrets

Edit `k8s/secrets.yaml` and replace the base64-encoded values with your actual credentials:

```bash
# Encode your values
echo -n "your-database-url" | base64
echo -n "your-redis-url" | base64
echo -n "your-minio-credentials" | base64
```

### 4. Deploy to Kubernetes

```bash
# Deploy everything
make k8s-deploy-all

# Check status
make k8s-status
```

## Detailed Deployment Steps

### Step 1: Prepare Your Environment

```bash
# Check if kubectl is configured
kubectl cluster-info

# Check available storage classes
kubectl get storageclass

# Install nginx-ingress (if not already installed)
kubectl apply -f https://raw.githubusercontent.com/kubernetes/ingress-nginx/controller-v1.8.1/deploy/static/provider/cloud/deploy.yaml
```

### Step 2: Build and Push Docker Images

```bash
# Build all images locally
make k8s-build-images

# Tag and push to your registry
docker tag anand-jewels/backend:latest your-registry.com/anand-jewels/backend:latest
docker tag anand-jewels/frontend:latest your-registry.com/anand-jewels/frontend:latest
docker tag anand-jewels/admin-portal:latest your-registry.com/anand-jewels/admin-portal:latest

docker push your-registry.com/anand-jewels/backend:latest
docker push your-registry.com/anand-jewels/frontend:latest
docker push your-registry.com/anand-jewels/admin-portal:latest

# Or use the make command
REGISTRY=your-registry.com make k8s-push-images
```

### Step 3: Configure Secrets and ConfigMaps

1. **Update secrets** in `k8s/secrets.yaml`:
   ```yaml
   # Example values (base64 encoded)
   DATABASE_URL: ********************************************************************************
   REDIS_URL: cmVkaXM6Ly86cGFzc3dvcmRAcmVkaXMtc2VydmljZTo2Mzc5
   ```

2. **Update ConfigMaps** in `k8s/configmap.yaml` if needed

3. **Update ingress** in `k8s/ingress.yaml` with your domain names

### Step 4: Deploy Services

```bash
# Deploy step by step
make k8s-deploy-namespace
make k8s-deploy-secrets
make k8s-deploy-config
make k8s-deploy-storage

# Wait for storage services to be ready
kubectl wait --for=condition=ready pod -l app=postgres -n jewelry-store --timeout=300s
kubectl wait --for=condition=ready pod -l app=redis -n jewelry-store --timeout=300s
kubectl wait --for=condition=ready pod -l app=minio -n jewelry-store --timeout=300s

# Deploy application services
make k8s-deploy-backend
kubectl wait --for=condition=ready pod -l app=backend -n jewelry-store --timeout=300s

make k8s-deploy-frontend
make k8s-deploy-ingress
```

### Step 5: Verify Deployment

```bash
# Check all resources
make k8s-status

# Check pod logs
make k8s-logs-backend
make k8s-logs-frontend
make k8s-logs-admin

# Port forward for local testing
make k8s-port-forward-frontend  # Access at http://localhost:3000
make k8s-port-forward-admin     # Access at http://localhost:3001
make k8s-port-forward-backend   # Access at http://localhost:8080
```

## Configuration

### Environment Variables

**Backend:**
- `DATABASE_URL`: PostgreSQL connection string
- `REDIS_URL`: Redis connection string
- `MINIO_ENDPOINT`: MinIO server endpoint
- `MINIO_ACCESS_KEY`: MinIO access key
- `MINIO_SECRET_KEY`: MinIO secret key
- `JWT_SECRET`: JWT signing secret

**Frontend/Admin Portal:**
- `VITE_API_URL`: Backend API URL
- `VITE_APP_NAME`: Application name
- `VITE_DEBUG_MODE`: Debug mode flag

### Ingress Configuration

The deployment includes two ingress configurations:

1. **Multi-domain setup** (`jewelry-store-ingress`):
   - `anand-jewels.com` → Frontend
   - `admin.anand-jewels.com` → Admin Portal
   - `api.anand-jewels.com` → Backend API

2. **Single domain setup** (`jewelry-store-single-domain`):
   - `anand-jewels.com/` → Frontend
   - `anand-jewels.com/admin` → Admin Portal
   - `anand-jewels.com/api` → Backend API

Choose the configuration that fits your needs and comment out the other.

### SSL Certificates

If you have cert-manager installed:

```bash
# Install cert-manager
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.13.0/cert-manager.yaml

# Create ClusterIssuer
kubectl apply -f - <<EOF
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
EOF
```

## Scaling

```bash
# Scale backend
REPLICAS=3 make k8s-scale-backend

# Scale frontend
REPLICAS=2 make k8s-scale-frontend

# Scale admin portal
REPLICAS=2 make k8s-scale-admin
```

## Monitoring and Maintenance

### View Logs

```bash
# Application logs
make k8s-logs-backend
make k8s-logs-frontend
make k8s-logs-admin

# All pods in namespace
kubectl logs -f --all-containers=true -n jewelry-store
```

### Database Operations

```bash
# Connect to PostgreSQL
make k8s-shell-postgres

# Connect to Redis
make k8s-shell-redis

# Backup database
kubectl exec deployment/postgres -n jewelry-store -- pg_dump -U jewelry jewelry > backup.sql

# Restore database
kubectl exec -i deployment/postgres -n jewelry-store -- psql -U jewelry jewelry < backup.sql
```

### Restart Services

```bash
# Restart individual services
make k8s-restart-backend
make k8s-restart-frontend
make k8s-restart-admin

# Rolling update with new image
kubectl set image deployment/backend backend=your-registry.com/anand-jewels/backend:v2.0.0 -n jewelry-store
```

## Troubleshooting

### Common Issues

1. **Pods not starting**: Check resource limits and node capacity
2. **Image pull errors**: Verify registry credentials and image names
3. **Database connection issues**: Check secrets and service names
4. **Ingress not working**: Verify ingress controller and DNS configuration

### Debug Commands

```bash
# Describe resources
kubectl describe pod <pod-name> -n jewelry-store
kubectl describe service <service-name> -n jewelry-store

# Check events
kubectl get events -n jewelry-store --sort-by='.lastTimestamp'

# Check resource usage
kubectl top pods -n jewelry-store
kubectl top nodes
```

## Cleanup

```bash
# Remove everything
make k8s-clean

# Remove specific components
kubectl delete -f k8s/ingress.yaml
kubectl delete -f k8s/frontend.yaml
kubectl delete -f k8s/admin-portal.yaml
kubectl delete -f k8s/backend.yaml
```

## Production Considerations

1. **Resource Limits**: Adjust CPU/memory limits based on your needs
2. **Persistent Storage**: Use appropriate storage classes for your cloud provider
3. **Backup Strategy**: Implement regular database and file backups
4. **Monitoring**: Add Prometheus/Grafana for monitoring
5. **Security**: Use network policies and pod security standards
6. **High Availability**: Deploy across multiple nodes/zones
