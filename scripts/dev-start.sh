#!/bin/bash

# Jewelry E-Commerce Development Environment Startup Script

set -e

echo "🚀 Starting Jewelry E-Commerce Development Environment..."

# Check if podman-compose is available
if command -v podman-compose &> /dev/null; then
    COMPOSE_CMD="podman-compose"
elif command -v docker-compose &> /dev/null; then
    COMPOSE_CMD="docker-compose"
else
    echo "❌ Neither podman-compose nor docker-compose found. Please install one of them."
    exit 1
fi

echo "📦 Using $COMPOSE_CMD for container orchestration"

# Create environment files if they don't exist
if [ ! -f backend/.env ]; then
    echo "📝 Creating backend/.env from template..."
    cp backend/.env.example backend/.env
    echo "⚠️  Please update backend/.env with your actual configuration values"
fi

if [ ! -f frontend/.env ]; then
    echo "📝 Creating frontend/.env from template..."
    cp frontend/.env.example frontend/.env
    echo "⚠️  Please update frontend/.env with your actual configuration values"
fi

# Choose compose file based on available tool
if [ "$COMPOSE_CMD" = "podman-compose" ]; then
    COMPOSE_FILE="podman-compose.yml"
else
    COMPOSE_FILE="docker-compose.yml"
fi

echo "🔧 Using compose file: $COMPOSE_FILE"

# Start the development environment
echo "🏗️  Building and starting containers..."
$COMPOSE_CMD -f $COMPOSE_FILE up --build -d

echo "⏳ Waiting for services to be healthy..."
sleep 10

# Check service health
echo "🔍 Checking service health..."
$COMPOSE_CMD -f $COMPOSE_FILE ps

echo ""
echo "✅ Development environment started successfully!"
echo ""
echo "🌐 Services available at:"
echo "   Frontend:  http://localhost:3000"
echo "   Backend:   http://localhost:8080"
echo "   Database:  localhost:5432"
echo "   Redis:     localhost:6379"
echo ""
echo "📋 Useful commands:"
echo "   View logs:     ./scripts/dev-logs.sh"
echo "   Stop services: ./scripts/dev-stop.sh"
echo "   Restart:       $COMPOSE_CMD -f $COMPOSE_FILE restart"
echo ""