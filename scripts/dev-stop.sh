#!/bin/bash

# Jewelry E-Commerce Development Environment Stop Script

set -e

echo "🛑 Stopping Jewelry E-Commerce Development Environment..."

# Check if podman-compose is available
if command -v podman-compose &> /dev/null; then
    COMPOSE_CMD="podman-compose"
    COMPOSE_FILE="podman-compose.yml"
elif command -v docker-compose &> /dev/null; then
    COMPOSE_CMD="docker-compose"
    COMPOSE_FILE="docker-compose.yml"
else
    echo "❌ Neither podman-compose nor docker-compose found."
    exit 1
fi

echo "📦 Using $COMPOSE_CMD with $COMPOSE_FILE"

# Stop and remove containers
echo "🔄 Stopping containers..."
$COMPOSE_CMD -f $COMPOSE_FILE down

echo ""
echo "✅ Development environment stopped successfully!"
echo ""
echo "📋 To start again: ./scripts/dev-start.sh"
echo "📋 To remove volumes: $COMPOSE_CMD -f $COMPOSE_FILE down -v"
echo ""