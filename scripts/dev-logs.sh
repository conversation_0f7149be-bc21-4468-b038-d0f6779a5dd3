#!/bin/bash

# Jewelry E-Commerce Development Environment Logs Script

set -e

# Check if podman-compose is available
if command -v podman-compose &> /dev/null; then
    COMPOSE_CMD="podman-compose"
    COMPOSE_FILE="podman-compose.yml"
elif command -v docker-compose &> /dev/null; then
    COMPOSE_CMD="docker-compose"
    COMPOSE_FILE="docker-compose.yml"
else
    echo "❌ Neither podman-compose nor docker-compose found."
    exit 1
fi

# Check if a specific service is requested
if [ $# -eq 0 ]; then
    echo "📋 Showing logs for all services..."
    echo "💡 Use: $0 <service-name> to view logs for a specific service"
    echo "   Available services: postgres, redis, backend, frontend, frontend-dev"
    echo ""
    $COMPOSE_CMD -f $COMPOSE_FILE logs -f
else
    SERVICE=$1
    echo "📋 Showing logs for service: $SERVICE"
    $COMPOSE_CMD -f $COMPOSE_FILE logs -f $SERVICE
fi