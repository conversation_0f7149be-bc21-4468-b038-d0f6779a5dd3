-- Seed data for jewelry e-commerce platform
-- Created: 2025-06-29

-- Insert sample admin user (you'll need to replace with actual Google OAuth data)
INSERT INTO users (id, google_id, email, name, role, is_active) VALUES
(uuid_generate_v4(), 'google_oauth_id_placeholder', '<EMAIL>', 'Admin User', 'super_admin', true)
ON CONFLICT (google_id) DO NOTHING;

-- Insert sample product categories and products
INSERT INTO products (
    id, sku, name, description, price, cost_price, category, subcategory,
    weight, material, gemstone, metal_purity, dimensions, availability,
    stock_quantity, min_stock_level, tags, is_featured, is_active
) VALUES
-- Gold Jewelry
(
    uuid_generate_v4(), 'GR001', 'Classic Gold Ring',
    'Beautiful 22K gold ring with traditional design',
    25000.00, 20000.00, 'rings', 'gold_rings',
    5.5, 'Gold', NULL, '22K',
    '{"length": 20, "width": 20, "height": 5, "unit": "mm"}',
    'available', 10, 2,
    ARRAY['gold', 'traditional', 'wedding', 'classic'],
    true, true
),
(
    uuid_generate_v4(), 'GN001', 'Gold Chain Necklace',
    'Elegant 18K gold chain necklace, perfect for daily wear',
    45000.00, 35000.00, 'necklaces', 'gold_chains',
    12.3, 'Gold', NULL, '18K',
    '{"length": 450, "width": 3, "height": 2, "unit": "mm"}',
    'available', 5, 1,
    ARRAY['gold', 'chain', 'daily_wear', 'elegant'],
    true, true
),
(
    uuid_generate_v4(), 'GE001', 'Diamond Gold Earrings',
    'Stunning diamond studded gold earrings',
    65000.00, 50000.00, 'earrings', 'diamond_earrings',
    8.2, 'Gold', 'Diamond', '18K',
    '{"length": 15, "width": 10, "height": 8, "unit": "mm"}',
    'available', 3, 1,
    ARRAY['gold', 'diamond', 'earrings', 'luxury', 'studded'],
    true, true
),

-- Silver Jewelry
(
    uuid_generate_v4(), 'SR001', 'Silver Ring with Stone',
    'Beautiful silver ring with semi-precious stone',
    3500.00, 2500.00, 'rings', 'silver_rings',
    4.2, 'Silver', 'Amethyst', '925',
    '{"length": 18, "width": 18, "height": 6, "unit": "mm"}',
    'available', 15, 3,
    ARRAY['silver', 'stone', 'amethyst', 'affordable'],
    false, true
),
(
    uuid_generate_v4(), 'SB001', 'Silver Bracelet',
    'Handcrafted silver bracelet with intricate design',
    8500.00, 6000.00, 'bracelets', 'silver_bracelets',
    25.5, 'Silver', NULL, '925',
    '{"length": 180, "width": 12, "height": 4, "unit": "mm"}',
    'available', 8, 2,
    ARRAY['silver', 'handcrafted', 'bracelet', 'intricate'],
    false, true
),

-- Gemstone Jewelry
(
    uuid_generate_v4(), 'GP001', 'Ruby Pendant',
    'Exquisite ruby pendant set in gold',
    85000.00, 65000.00, 'pendants', 'gemstone_pendants',
    6.8, 'Gold', 'Ruby', '18K',
    '{"length": 25, "width": 15, "height": 8, "unit": "mm"}',
    'available', 2, 1,
    ARRAY['ruby', 'pendant', 'gold', 'precious', 'luxury'],
    true, true
),
(
    uuid_generate_v4(), 'GS001', 'Emerald Stud Earrings',
    'Natural emerald stud earrings in gold setting',
    120000.00, 95000.00, 'earrings', 'gemstone_earrings',
    4.5, 'Gold', 'Emerald', '18K',
    '{"length": 8, "width": 8, "height": 6, "unit": "mm"}',
    'available', 1, 1,
    ARRAY['emerald', 'stud', 'earrings', 'natural', 'luxury'],
    true, true
),

-- Bangles and Bracelets
(
    uuid_generate_v4(), 'GB001', 'Gold Bangle Set',
    'Set of 2 traditional gold bangles',
    95000.00, 75000.00, 'bangles', 'gold_bangles',
    45.2, 'Gold', NULL, '22K',
    '{"length": 65, "width": 65, "height": 8, "unit": "mm"}',
    'available', 4, 1,
    ARRAY['gold', 'bangles', 'traditional', 'set', 'wedding'],
    true, true
),

-- Anklets
(
    uuid_generate_v4(), 'SA001', 'Silver Anklet',
    'Delicate silver anklet with small bells',
    4500.00, 3200.00, 'anklets', 'silver_anklets',
    18.5, 'Silver', NULL, '925',
    '{"length": 240, "width": 3, "height": 2, "unit": "mm"}',
    'available', 12, 3,
    ARRAY['silver', 'anklet', 'bells', 'delicate', 'traditional'],
    false, true
),

-- Nose Pins
(
    uuid_generate_v4(), 'GNP001', 'Gold Nose Pin',
    'Small gold nose pin with diamond',
    15000.00, 12000.00, 'nose_pins', 'gold_nose_pins',
    0.8, 'Gold', 'Diamond', '18K',
    '{"length": 6, "width": 3, "height": 2, "unit": "mm"}',
    'available', 20, 5,
    ARRAY['gold', 'nose_pin', 'diamond', 'small', 'delicate'],
    false, true
);

-- Insert sample collections
INSERT INTO collections (id, slug, name, description, is_active, is_public, view_count) VALUES
(
    uuid_generate_v4(), 'bridal-collection-2024',
    'Bridal Collection 2024',
    'Exquisite jewelry pieces perfect for your special day',
    true, true, 0
),
(
    uuid_generate_v4(), 'daily-wear-essentials',
    'Daily Wear Essentials',
    'Elegant jewelry for everyday styling',
    true, true, 0
),
(
    uuid_generate_v4(), 'luxury-gemstone-collection',
    'Luxury Gemstone Collection',
    'Premium gemstone jewelry for special occasions',
    true, true, 0
);

-- Link products to collections
INSERT INTO collection_products (collection_id, product_id, display_order, is_featured)
SELECT
    c.id,
    p.id,
    ROW_NUMBER() OVER (PARTITION BY c.id ORDER BY p.price DESC),
    CASE WHEN p.is_featured THEN true ELSE false END
FROM collections c
CROSS JOIN products p
WHERE
    (c.slug = 'bridal-collection-2024' AND p.tags && ARRAY['wedding', 'luxury', 'traditional']) OR
    (c.slug = 'daily-wear-essentials' AND p.tags && ARRAY['daily_wear', 'elegant', 'affordable']) OR
    (c.slug = 'luxury-gemstone-collection' AND p.tags && ARRAY['luxury', 'precious', 'gemstone']);

-- Insert sample customers
INSERT INTO customers (id, name, email, phone, address, notes) VALUES
(
    uuid_generate_v4(), 'Priya Sharma', '<EMAIL>', '+91-9876543210',
    '{"street": "123 MG Road", "city": "Mumbai", "state": "Maharashtra", "country": "India", "postal_code": "400001"}',
    'Regular customer, prefers gold jewelry'
),
(
    uuid_generate_v4(), 'Rajesh Kumar', '<EMAIL>', '+91-9876543211',
    '{"street": "456 Brigade Road", "city": "Bangalore", "state": "Karnataka", "country": "India", "postal_code": "560001"}',
    'Interested in gemstone jewelry'
);

-- Insert sample orders
INSERT INTO orders (
    id, order_number, customer_name, customer_phone, customer_email,
    customer_address, subtotal, tax_amount, total_amount, status
) VALUES
(
    uuid_generate_v4(), 'AJ2024001', 'Priya Sharma', '+91-9876543210', '<EMAIL>',
    '{"street": "123 MG Road", "city": "Mumbai", "state": "Maharashtra", "country": "India", "postal_code": "400001"}',
    25000.00, 0.00, 25000.00, 'confirmed'
),
(
    uuid_generate_v4(), 'AJ2024002', 'Rajesh Kumar', '+91-9876543211', '<EMAIL>',
    '{"street": "456 Brigade Road", "city": "Bangalore", "state": "Karnataka", "country": "India", "postal_code": "560001"}',
    85000.00, 0.00, 85000.00, 'pending'
);

-- Insert order items
INSERT INTO order_items (
    id, order_id, product_id, product_name, product_sku,
    unit_price, quantity, total_price, product_weight, product_material
)
SELECT
    uuid_generate_v4(),
    o.id,
    p.id,
    p.name,
    p.sku,
    p.price,
    1,
    p.price,
    p.weight,
    p.material
FROM orders o
CROSS JOIN products p
WHERE
    (o.order_number = 'AJ2024001' AND p.sku = 'GR001') OR
    (o.order_number = 'AJ2024002' AND p.sku = 'GP001');

-- Update customer statistics
UPDATE customers SET
    total_orders = (SELECT COUNT(*) FROM orders WHERE customer_phone = customers.phone),
    total_spent = (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE customer_phone = customers.phone),
    first_order_at = (SELECT MIN(created_at) FROM orders WHERE customer_phone = customers.phone),
    last_order_at = (SELECT MAX(created_at) FROM orders WHERE customer_phone = customers.phone);

-- Insert sample analytics data
INSERT INTO analytics (event_type, entity_type, entity_id, session_id, ip_address, metadata)
SELECT
    'view',
    'collection',
    c.id,
    'session_' || uuid_generate_v4(),
    ('192.168.1.' || (random() * 255)::int)::inet,
    '{"user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}'
FROM collections c
WHERE c.is_public = true;