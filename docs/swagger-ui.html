<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jewelry E-Commerce Platform API Documentation</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@5.10.5/swagger-ui.css" />
    <style>
        html {
            box-sizing: border-box;
            overflow: -moz-scrollbars-vertical;
            overflow-y: scroll;
        }
        *, *:before, *:after {
            box-sizing: inherit;
        }
        body {
            margin: 0;
            background: #fafafa;
        }
        .swagger-ui .topbar {
            background-color: #1f2937;
        }
        .swagger-ui .topbar .download-url-wrapper .select-label {
            color: #ffffff;
        }
        .swagger-ui .topbar .download-url-wrapper input[type=text] {
            border: 2px solid #374151;
        }
        .custom-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            margin-bottom: 0;
        }
        .custom-header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .custom-header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        .api-info {
            background: white;
            padding: 20px;
            margin: 0;
            border-bottom: 1px solid #e5e7eb;
        }
        .api-info h2 {
            margin-top: 0;
            color: #1f2937;
        }
        .api-stats {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin-top: 15px;
        }
        .stat-item {
            background: #f3f4f6;
            padding: 10px 15px;
            border-radius: 8px;
            text-align: center;
            min-width: 120px;
        }
        .stat-number {
            font-size: 1.5em;
            font-weight: bold;
            color: #667eea;
        }
        .stat-label {
            font-size: 0.9em;
            color: #6b7280;
            margin-top: 5px;
        }
        .quick-links {
            background: #f9fafb;
            padding: 15px 20px;
            border-bottom: 1px solid #e5e7eb;
        }
        .quick-links h3 {
            margin: 0 0 10px 0;
            color: #1f2937;
        }
        .links-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }
        .link-item {
            background: white;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
            text-decoration: none;
            color: #374151;
            transition: all 0.2s;
        }
        .link-item:hover {
            border-color: #667eea;
            background: #f8faff;
        }
        .link-title {
            font-weight: 600;
            color: #667eea;
        }
        .link-desc {
            font-size: 0.9em;
            color: #6b7280;
            margin-top: 2px;
        }
    </style>
</head>
<body>
    <div class="custom-header">
        <h1>🏺 Jewelry E-Commerce Platform</h1>
        <p>Comprehensive REST API Documentation</p>
    </div>

    <div class="api-info">
        <h2>API Overview</h2>
        <p>A complete REST API for managing jewelry products, collections, orders, customers, and inventory. Built with Go, PostgreSQL, Redis, and MinIO for a modern e-commerce experience.</p>
        
        <div class="api-stats">
            <div class="stat-item">
                <div class="stat-number">28+</div>
                <div class="stat-label">API Endpoints</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">6</div>
                <div class="stat-label">Core Modules</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">11</div>
                <div class="stat-label">Database Tables</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">100%</div>
                <div class="stat-label">OpenAPI 3.0</div>
            </div>
        </div>
    </div>

    <div class="quick-links">
        <h3>🚀 Quick Access</h3>
        <div class="links-grid">
            <a href="#/Products" class="link-item">
                <div class="link-title">📦 Products</div>
                <div class="link-desc">Manage jewelry items with images</div>
            </a>
            <a href="#/Collections" class="link-item">
                <div class="link-title">📚 Collections</div>
                <div class="link-desc">Curated product groups & sharing</div>
            </a>
            <a href="#/Orders" class="link-item">
                <div class="link-title">🛒 Orders</div>
                <div class="link-desc">Order processing & management</div>
            </a>
            <a href="#/Customers" class="link-item">
                <div class="link-title">👥 Customers</div>
                <div class="link-desc">Customer information & statistics</div>
            </a>
            <a href="#/Inventory" class="link-item">
                <div class="link-title">📊 Inventory</div>
                <div class="link-desc">Stock tracking & alerts</div>
            </a>
            <a href="#/Upload" class="link-item">
                <div class="link-title">🖼️ Upload</div>
                <div class="link-desc">Image management & variants</div>
            </a>
        </div>
    </div>

    <div id="swagger-ui"></div>

    <script src="https://unpkg.com/swagger-ui-dist@5.10.5/swagger-ui-bundle.js"></script>
    <script src="https://unpkg.com/swagger-ui-dist@5.10.5/swagger-ui-standalone-preset.js"></script>
    <script>
        // Load the OpenAPI specification
        const spec = `openapi: 3.0.3
info:
  title: Jewelry E-Commerce Platform API
  description: |
    A comprehensive REST API for a curated jewelry showcase platform where admins upload items, 
    create collections with shareable links, customers select items and place orders, 
    and admins confirm orders via phone calls.
    
    ## Features
    - Product management with image upload
    - Collection management with shareable URLs
    - Order processing and customer management
    - Inventory tracking with alerts
    - Image storage with multiple size variants
    
    ## Authentication
    Currently, most endpoints are public for development. Authentication will be added in Phase 2.
    
    ## Image Storage
    Images are stored using MinIO (local development) or AWS S3 (production) with automatic
    generation of multiple size variants (thumbnail, small, medium, large).
    
  version: 1.0.0
  contact:
    name: Anand Jewels API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8080/api/v1
    description: Local development server
  - url: https://api.anandjewels.com/v1
    description: Production server

# Note: This is a simplified version for the HTML viewer
# For the complete specification, see api-spec.yaml
`;

        window.onload = function() {
            // Try to load the actual YAML file first, fallback to embedded spec
            fetch('./api-spec.yaml')
                .then(response => response.text())
                .then(yamlText => {
                    initSwaggerUI(yamlText);
                })
                .catch(error => {
                    console.log('Could not load api-spec.yaml, using embedded spec');
                    initSwaggerUI(spec);
                });
        };

        function initSwaggerUI(specContent) {
            const ui = SwaggerUIBundle({
                spec: jsyaml.load ? jsyaml.load(specContent) : specContent,
                dom_id: '#swagger-ui',
                deepLinking: true,
                presets: [
                    SwaggerUIBundle.presets.apis,
                    SwaggerUIStandalonePreset
                ],
                plugins: [
                    SwaggerUIBundle.plugins.DownloadUrl
                ],
                layout: "StandaloneLayout",
                defaultModelsExpandDepth: 1,
                defaultModelExpandDepth: 1,
                docExpansion: "list",
                filter: true,
                showExtensions: true,
                showCommonExtensions: true,
                tryItOutEnabled: true
            });
        }
    </script>
    
    <!-- Include js-yaml for YAML parsing -->
    <script src="https://unpkg.com/js-yaml@4.1.0/dist/js-yaml.min.js"></script>
</body>
</html>
