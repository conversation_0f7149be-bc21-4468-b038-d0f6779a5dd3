# Jewelry E-Commerce Platform API Documentation

## Overview

This directory contains the comprehensive API documentation for the Jewelry E-Commerce Platform. The platform provides a complete REST API for managing jewelry products, collections, orders, customers, and inventory.

## API Specification

The main API specification is defined in OpenAPI 3.0.3 format:

- **File**: `api-spec.yaml`
- **Format**: OpenAPI 3.0.3 (Swagger)
- **Base URL**: `http://localhost:8080/api/v1` (development)

## Viewing the Documentation

### Option 1: Swagger UI (Recommended)

You can view the interactive API documentation using Swagger UI:

1. **Online Swagger Editor**: 
   - Go to [editor.swagger.io](https://editor.swagger.io/)
   - Copy the contents of `api-spec.yaml` and paste it in the editor
   - View the interactive documentation on the right panel

2. **Local Swagger UI**:
   ```bash
   # Using Docker
   docker run -p 8081:8080 -e SWAGGER_JSON=/api-spec.yaml -v $(pwd)/docs:/usr/share/nginx/html swaggerapi/swagger-ui
   
   # Then open http://localhost:8081 in your browser
   ```

3. **VS Code Extension**:
   - Install the "Swagger Viewer" extension
   - Open `api-spec.yaml` in VS Code
   - Press `Shift+Alt+P` and select "Preview Swagger"

### Option 2: Redoc

For a different documentation style, you can use Redoc:

```bash
# Using Docker
docker run -p 8082:80 -e SPEC_URL=/api-spec.yaml -v $(pwd)/docs:/usr/share/nginx/html redocly/redoc

# Then open http://localhost:8082 in your browser
```

## API Endpoints Summary

### 🏥 Health & Status
- `GET /health` - Health check
- `GET /db-info` - Database information

### 📦 Products (6 endpoints)
- `GET /products` - List products with filtering and pagination
- `POST /products` - Create new product
- `GET /products/{id}` - Get single product
- `POST /products/{id}/images` - Upload product image
- `GET /products/{id}/images/{image_id}` - Get image info
- `DELETE /products/{id}/images/{image_id}` - Delete image

### 📚 Collections (7 endpoints)
- `GET /collections` - List collections
- `POST /collections` - Create collection
- `GET /collections/{id}` - Get collection
- `GET /collections/slug/{slug}` - Get collection by slug (public sharing)
- `GET /collections/{id}/products` - Get collection with products
- `POST /collections/{id}/products` - Add product to collection
- `DELETE /collections/{id}/products/{product_id}` - Remove product from collection

### 🛒 Orders (3 endpoints)
- `GET /orders` - List orders with filtering
- `POST /orders` - Create new order
- `GET /orders/{id}` - Get order details

### 👥 Customers (4 endpoints)
- `GET /customers` - List customers
- `POST /customers` - Create customer
- `GET /customers/{id}` - Get customer
- `PUT /customers/{id}` - Update customer

### 📊 Inventory (5 endpoints)
- `GET /inventory` - Get inventory status
- `PUT /inventory/{id}` - Update product inventory
- `GET /inventory/logs` - Get inventory change logs
- `GET /inventory/alerts/low-stock` - Get low stock alerts
- `GET /inventory/alerts/out-of-stock` - Get out of stock alerts

## Key Features

### 🔍 Advanced Filtering & Search
- **Products**: Filter by category, subcategory, price range, availability
- **Collections**: Filter by public status, active status
- **Orders**: Filter by customer, status, date range
- **Customers**: Filter by active status
- **Inventory**: Filter by category, stock levels

### 📄 Pagination
All list endpoints support pagination with:
- `page` parameter (1-based, default: 1)
- `limit` parameter (1-100, default: 20)
- `X-Total-Count` header in responses

### 🔤 Sorting
Most list endpoints support sorting with:
- `sort_by` parameter (field name)
- `sort_order` parameter (`asc` or `desc`)

### 🖼️ Image Management
- **Multi-size variants**: Automatic generation of thumbnail, small, medium, large sizes
- **Format support**: JPEG, PNG, WebP
- **Storage**: MinIO (development) / AWS S3 (production)
- **Size limit**: 10MB per image
- **Optimization**: Automatic compression and quality optimization

### 📦 Business Logic
- **Order Management**: Automatic inventory deduction and customer statistics updates
- **Inventory Tracking**: Real-time stock levels with low stock alerts
- **Collection Sharing**: Public URLs with slug-based access
- **Transaction Safety**: Database transactions for data consistency

## Authentication

**Current Status**: Most endpoints are currently public for development purposes.

**Planned**: Google OAuth 2.0 integration (Phase 2)
- JWT Bearer tokens
- Role-based access control (admin vs customer)
- Protected admin endpoints

## Error Handling

The API uses consistent error response format:

```json
{
  "error": "error_code",
  "message": "Human readable message",
  "code": 400,
  "details": "Additional error details"
}
```

### Common Error Codes
- `400` - Bad Request (validation errors)
- `404` - Not Found (resource doesn't exist)
- `409` - Conflict (duplicate resources)
- `500` - Internal Server Error

## Data Models

### Core Entities
- **Product**: Jewelry items with detailed metadata
- **Collection**: Curated product groups with sharing capabilities
- **Order**: Customer orders with items and delivery information
- **Customer**: Customer information and statistics
- **Inventory**: Stock levels and change tracking

### Relationships
- Products can belong to multiple Collections (many-to-many)
- Orders belong to Customers (one-to-many)
- Orders contain multiple OrderItems (one-to-many)
- Products have multiple Images (one-to-many)
- Inventory changes are logged per Product (one-to-many)

## Development

### Testing the API

1. **Start the development environment**:
   ```bash
   ./scripts/dev-start.sh
   ```

2. **Test endpoints using curl**:
   ```bash
   # Get all products
   curl http://localhost:8080/api/v1/products
   
   # Get health status
   curl http://localhost:8080/api/v1/health
   
   # Create a product
   curl -X POST http://localhost:8080/api/v1/products \
     -H "Content-Type: application/json" \
     -d '{"sku":"TEST001","name":"Test Product","price":1000,"category":"rings","stock_quantity":5}'
   ```

3. **Access services**:
   - **API**: http://localhost:8080
   - **MinIO Console**: http://localhost:9001 (minioadmin/minioadmin123)
   - **Database**: localhost:5432 (postgres/password)

### API Validation

The OpenAPI specification can be validated using:

```bash
# Using swagger-codegen-cli
swagger-codegen-cli validate -i docs/api-spec.yaml

# Using openapi-generator-cli
openapi-generator-cli validate -i docs/api-spec.yaml
```

## Contributing

When adding new endpoints:

1. Update the OpenAPI specification in `api-spec.yaml`
2. Add appropriate schemas and examples
3. Document any new query parameters or request/response formats
4. Test the endpoints thoroughly
5. Update this README if needed

## Support

For API support or questions:
- **Email**: <EMAIL>
- **Documentation**: This README and the OpenAPI specification
- **Issues**: Create issues in the project repository
