openapi: 3.0.3
info:
  title: Jewelry E-Commerce Platform API
  description: |
    A comprehensive REST API for a curated jewelry showcase platform where admins upload items, 
    create collections with shareable links, customers select items and place orders, 
    and admins confirm orders via phone calls.
    
    ## Features
    - Product management with image upload
    - Collection management with shareable URLs
    - Order processing and customer management
    - Inventory tracking with alerts
    - Image storage with multiple size variants
    
    ## Authentication
    Currently, most endpoints are public for development. Authentication will be added in Phase 2.
    
    ## Image Storage
    Images are stored using MinIO (local development) or AWS S3 (production) with automatic
    generation of multiple size variants (thumbnail, small, medium, large).
    
  version: 1.0.0
  contact:
    name: Anand Jewels API Support
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:8080/api/v1
    description: Local development server
  - url: https://api.anandjewels.com/v1
    description: Production server

tags:
  - name: Health
    description: Health check and system status
  - name: Products
    description: Product management operations
  - name: Collections
    description: Collection management and sharing
  - name: Orders
    description: Order processing and management
  - name: Customers
    description: Customer management
  - name: Inventory
    description: Inventory tracking and alerts
  - name: Upload
    description: Image upload and management

paths:
  /health:
    get:
      tags:
        - Health
      summary: Health check
      description: Check the health status of the API and its dependencies
      responses:
        '200':
          description: Service is healthy
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: string
                    example: healthy
                  service:
                    type: string
                    example: jewelry-backend
                  version:
                    type: string
                    example: 1.0.0
                  database:
                    type: string
                    example: connected
                  redis:
                    type: string
                    example: connected

  /db-info:
    get:
      tags:
        - Health
      summary: Database information
      description: Get detailed database and Redis connection information
      responses:
        '200':
          description: Database information retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  postgres_info:
                    type: object
                  redis_info:
                    type: object

  /products:
    get:
      tags:
        - Products
      summary: List products
      description: Retrieve a paginated list of products with optional filtering and search
      parameters:
        - name: page
          in: query
          description: Page number (1-based)
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: category
          in: query
          description: Filter by product category
          schema:
            type: string
            enum: [rings, necklaces, earrings, bracelets, bangles, anklets, nose_pins, pendants]
        - name: subcategory
          in: query
          description: Filter by product subcategory
          schema:
            type: string
        - name: availability
          in: query
          description: Filter by availability status
          schema:
            type: string
            enum: [available, out_of_stock, discontinued]
        - name: min_price
          in: query
          description: Minimum price filter
          schema:
            type: number
            format: float
        - name: max_price
          in: query
          description: Maximum price filter
          schema:
            type: number
            format: float
        - name: search
          in: query
          description: Search in product name and description
          schema:
            type: string
        - name: sort_by
          in: query
          description: Sort field
          schema:
            type: string
            enum: [name, price, created_at, updated_at]
            default: created_at
        - name: sort_order
          in: query
          description: Sort order
          schema:
            type: string
            enum: [asc, desc]
            default: desc
      responses:
        '200':
          description: Products retrieved successfully
          headers:
            X-Total-Count:
              description: Total number of products
              schema:
                type: integer
          content:
            application/json:
              schema:
                type: object
                properties:
                  products:
                    type: array
                    items:
                      $ref: '#/components/schemas/Product'
                  total:
                    type: integer
                  page:
                    type: integer
                  limit:
                    type: integer
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      tags:
        - Products
      summary: Create product
      description: Create a new product
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateProductRequest'
      responses:
        '201':
          description: Product created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Product'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /products/{id}:
    get:
      tags:
        - Products
      summary: Get product
      description: Retrieve a single product by ID with images
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Product retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Product'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /products/{id}/images:
    post:
      tags:
        - Upload
      summary: Upload product image
      description: Upload an image file for a specific product with automatic variant generation
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                image:
                  type: string
                  format: binary
                  description: Image file (JPEG, PNG, WebP, max 10MB)
                is_primary:
                  type: boolean
                  description: Set as primary image
                  default: false
                alt_text:
                  type: string
                  description: Alt text for accessibility
              required:
                - image
      responses:
        '200':
          description: Image uploaded successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  image_id:
                    type: string
                    format: uuid
                  storage_id:
                    type: string
                  image:
                    $ref: '#/components/schemas/UploadedImage'
                  is_primary:
                    type: boolean
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /products/{id}/images/{image_id}:
    get:
      tags:
        - Upload
      summary: Get image information
      description: Get detailed information about an uploaded image
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
            format: uuid
        - name: image_id
          in: path
          required: true
          description: Image ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Image information retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  id:
                    type: string
                    format: uuid
                  product_id:
                    type: string
                    format: uuid
                  image_url:
                    type: string
                    format: uri
                  alt_text:
                    type: string
                  display_order:
                    type: integer
                  is_primary:
                    type: boolean
                  storage_path:
                    type: string
                  created_at:
                    type: string
                    format: date-time
                  updated_at:
                    type: string
                    format: date-time
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    delete:
      tags:
        - Upload
      summary: Delete product image
      description: Delete a specific product image and all its variants
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
            format: uuid
        - name: image_id
          in: path
          required: true
          description: Image ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Image deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Image deleted successfully
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /collections:
    get:
      tags:
        - Collections
      summary: List collections
      description: Retrieve a paginated list of collections with optional filtering
      parameters:
        - name: page
          in: query
          description: Page number (1-based)
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: is_public
          in: query
          description: Filter by public status
          schema:
            type: boolean
        - name: is_active
          in: query
          description: Filter by active status
          schema:
            type: boolean
        - name: search
          in: query
          description: Search in collection name and description
          schema:
            type: string
        - name: sort_by
          in: query
          description: Sort field
          schema:
            type: string
            enum: [name, created_at, view_count]
            default: created_at
        - name: sort_order
          in: query
          description: Sort order
          schema:
            type: string
            enum: [asc, desc]
            default: desc
      responses:
        '200':
          description: Collections retrieved successfully
          headers:
            X-Total-Count:
              description: Total number of collections
              schema:
                type: integer
          content:
            application/json:
              schema:
                type: object
                properties:
                  collections:
                    type: array
                    items:
                      $ref: '#/components/schemas/Collection'
                  total:
                    type: integer
                  page:
                    type: integer
                  limit:
                    type: integer
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      tags:
        - Collections
      summary: Create collection
      description: Create a new product collection
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCollectionRequest'
      responses:
        '201':
          description: Collection created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Collection'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /collections/{id}:
    get:
      tags:
        - Collections
      summary: Get collection
      description: Retrieve a single collection by ID
      parameters:
        - name: id
          in: path
          required: true
          description: Collection ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Collection retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Collection'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /collections/slug/{slug}:
    get:
      tags:
        - Collections
      summary: Get collection by slug
      description: Retrieve a public collection by its slug (for shareable URLs)
      parameters:
        - name: slug
          in: path
          required: true
          description: Collection slug
          schema:
            type: string
      responses:
        '200':
          description: Collection retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Collection'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /collections/{id}/products:
    get:
      tags:
        - Collections
      summary: Get collection with products
      description: Retrieve a collection with all its products
      parameters:
        - name: id
          in: path
          required: true
          description: Collection ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Collection with products retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CollectionWithProducts'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      tags:
        - Collections
      summary: Add product to collection
      description: Add a product to a collection
      parameters:
        - name: id
          in: path
          required: true
          description: Collection ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - product_id
              properties:
                product_id:
                  type: string
                  format: uuid
                display_order:
                  type: integer
                  minimum: 1
                is_featured:
                  type: boolean
                  default: false
      responses:
        '200':
          description: Product added to collection successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  collection_product:
                    $ref: '#/components/schemas/CollectionProduct'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '409':
          description: Product already in collection
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /collections/{id}/products/{product_id}:
    delete:
      tags:
        - Collections
      summary: Remove product from collection
      description: Remove a product from a collection
      parameters:
        - name: id
          in: path
          required: true
          description: Collection ID
          schema:
            type: string
            format: uuid
        - name: product_id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Product removed from collection successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Product removed from collection successfully
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /orders:
    get:
      tags:
        - Orders
      summary: List orders
      description: Retrieve a paginated list of orders with optional filtering
      parameters:
        - name: page
          in: query
          description: Page number (1-based)
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: customer_id
          in: query
          description: Filter by customer ID
          schema:
            type: string
            format: uuid
        - name: status
          in: query
          description: Filter by order status
          schema:
            type: string
            enum: [pending, confirmed, processing, shipped, delivered, cancelled]
        - name: date_from
          in: query
          description: Filter orders from date (YYYY-MM-DD)
          schema:
            type: string
            format: date
        - name: date_to
          in: query
          description: Filter orders to date (YYYY-MM-DD)
          schema:
            type: string
            format: date
        - name: search
          in: query
          description: Search in order number and customer details
          schema:
            type: string
      responses:
        '200':
          description: Orders retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  orders:
                    type: array
                    items:
                      $ref: '#/components/schemas/Order'
                  total:
                    type: integer
                  page:
                    type: integer
                  limit:
                    type: integer

    post:
      tags:
        - Orders
      summary: Create order
      description: Create a new order with automatic inventory management
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOrderRequest'
      responses:
        '201':
          description: Order created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Order'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /orders/{id}:
    get:
      tags:
        - Orders
      summary: Get order
      description: Retrieve a single order by ID with items and customer details
      parameters:
        - name: id
          in: path
          required: true
          description: Order ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Order retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Order'
        '404':
          $ref: '#/components/responses/NotFound'

  /customers:
    get:
      tags:
        - Customers
      summary: List customers
      description: Retrieve a paginated list of customers with optional filtering
      parameters:
        - name: page
          in: query
          description: Page number (1-based)
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: is_active
          in: query
          description: Filter by active status
          schema:
            type: boolean
        - name: search
          in: query
          description: Search in customer name, email, and phone
          schema:
            type: string
      responses:
        '200':
          description: Customers retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  customers:
                    type: array
                    items:
                      $ref: '#/components/schemas/Customer'
                  total:
                    type: integer
                  page:
                    type: integer
                  limit:
                    type: integer

    post:
      tags:
        - Customers
      summary: Create customer
      description: Create a new customer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCustomerRequest'
      responses:
        '201':
          description: Customer created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Customer'

  /customers/{id}:
    get:
      tags:
        - Customers
      summary: Get customer
      description: Retrieve a single customer by ID
      parameters:
        - name: id
          in: path
          required: true
          description: Customer ID
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Customer retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Customer'
        '404':
          $ref: '#/components/responses/NotFound'

    put:
      tags:
        - Customers
      summary: Update customer
      description: Update customer information
      parameters:
        - name: id
          in: path
          required: true
          description: Customer ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateCustomerRequest'
      responses:
        '200':
          description: Customer updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Customer'

  /inventory:
    get:
      tags:
        - Inventory
      summary: Get inventory status
      description: Retrieve inventory status for all products with optional filtering
      parameters:
        - name: page
          in: query
          description: Page number (1-based)
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: category
          in: query
          description: Filter by product category
          schema:
            type: string
        - name: low_stock_only
          in: query
          description: Show only low stock items
          schema:
            type: boolean
        - name: out_of_stock
          in: query
          description: Show only out of stock items
          schema:
            type: boolean
        - name: search
          in: query
          description: Search in product name and SKU
          schema:
            type: string
      responses:
        '200':
          description: Inventory status retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  inventory:
                    type: array
                    items:
                      $ref: '#/components/schemas/InventoryStatus'
                  total:
                    type: integer
                  page:
                    type: integer
                  limit:
                    type: integer

  /inventory/{id}:
    put:
      tags:
        - Inventory
      summary: Update product inventory
      description: Update inventory quantity for a specific product
      parameters:
        - name: id
          in: path
          required: true
          description: Product ID
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateInventoryRequest'
      responses:
        '200':
          description: Inventory updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/InventoryLog'

  /inventory/logs:
    get:
      tags:
        - Inventory
      summary: Get inventory logs
      description: Retrieve inventory change logs with optional filtering
      parameters:
        - name: page
          in: query
          description: Page number (1-based)
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          description: Number of items per page
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: product_id
          in: query
          description: Filter by product ID
          schema:
            type: string
            format: uuid
        - name: date_from
          in: query
          description: Filter logs from date (YYYY-MM-DD)
          schema:
            type: string
            format: date
        - name: date_to
          in: query
          description: Filter logs to date (YYYY-MM-DD)
          schema:
            type: string
            format: date
      responses:
        '200':
          description: Inventory logs retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  logs:
                    type: array
                    items:
                      $ref: '#/components/schemas/InventoryLog'
                  total:
                    type: integer
                  page:
                    type: integer
                  limit:
                    type: integer

  /inventory/alerts/low-stock:
    get:
      tags:
        - Inventory
      summary: Get low stock alerts
      description: Get products that are at or below minimum stock level
      responses:
        '200':
          description: Low stock alerts retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  alerts:
                    type: array
                    items:
                      $ref: '#/components/schemas/InventoryStatus'
                  total:
                    type: integer

  /inventory/alerts/out-of-stock:
    get:
      tags:
        - Inventory
      summary: Get out of stock alerts
      description: Get products that are completely out of stock
      responses:
        '200':
          description: Out of stock alerts retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  alerts:
                    type: array
                    items:
                      $ref: '#/components/schemas/InventoryStatus'
                  total:
                    type: integer

components:
  schemas:
    Product:
      type: object
      properties:
        id:
          type: string
          format: uuid
        sku:
          type: string
        name:
          type: string
        description:
          type: string
        price:
          type: number
          format: float
        cost_price:
          type: number
          format: float
        category:
          type: string
          enum: [rings, necklaces, earrings, bracelets, bangles, anklets, nose_pins, pendants]
        subcategory:
          type: string
        weight:
          type: number
          format: float
        material:
          type: string
        gemstone:
          type: string
        metal_purity:
          type: string
        dimensions:
          type: object
          properties:
            length:
              type: number
            width:
              type: number
            height:
              type: number
            unit:
              type: string
        availability:
          type: string
          enum: [available, out_of_stock, discontinued]
        stock_quantity:
          type: integer
        min_stock_level:
          type: integer
        tags:
          type: array
          items:
            type: string
        is_featured:
          type: boolean
        is_active:
          type: boolean
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        images:
          type: array
          items:
            $ref: '#/components/schemas/ProductImage'
        primary_image:
          $ref: '#/components/schemas/ProductImage'

    CreateProductRequest:
      type: object
      required:
        - sku
        - name
        - price
        - category
        - stock_quantity
      properties:
        sku:
          type: string
        name:
          type: string
        description:
          type: string
        price:
          type: number
          format: float
          minimum: 0
        cost_price:
          type: number
          format: float
          minimum: 0
        category:
          type: string
          enum: [rings, necklaces, earrings, bracelets, bangles, anklets, nose_pins, pendants]
        subcategory:
          type: string
        weight:
          type: number
          format: float
          minimum: 0
        material:
          type: string
        gemstone:
          type: string
        metal_purity:
          type: string
        dimensions:
          type: object
          properties:
            length:
              type: number
            width:
              type: number
            height:
              type: number
            unit:
              type: string
        availability:
          type: string
          enum: [available, out_of_stock, discontinued]
          default: available
        stock_quantity:
          type: integer
          minimum: 0
        min_stock_level:
          type: integer
          minimum: 0
          default: 1
        tags:
          type: array
          items:
            type: string
        is_featured:
          type: boolean
          default: false

    ProductImage:
      type: object
      properties:
        id:
          type: string
          format: uuid
        image_url:
          type: string
          format: uri
        alt_text:
          type: string
        display_order:
          type: integer
        is_primary:
          type: boolean
        created_at:
          type: string
          format: date-time

    UploadedImage:
      type: object
      properties:
        id:
          type: string
        original:
          $ref: '#/components/schemas/ImageVariant'
        variants:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/ImageVariant'
        created_at:
          type: string
          format: date-time

    ImageVariant:
      type: object
      properties:
        url:
          type: string
          format: uri
        width:
          type: integer
        height:
          type: integer
        size:
          type: integer
          format: int64
        filename:
          type: string

    Collection:
      type: object
      properties:
        id:
          type: string
          format: uuid
        slug:
          type: string
        name:
          type: string
        description:
          type: string
        cover_image_url:
          type: string
          format: uri
        is_public:
          type: boolean
        is_active:
          type: boolean
        expires_at:
          type: string
          format: date-time
        view_count:
          type: integer
        share_count:
          type: integer
        product_count:
          type: integer
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    CreateCollectionRequest:
      type: object
      required:
        - slug
        - name
      properties:
        slug:
          type: string
          pattern: '^[a-z0-9-]+$'
        name:
          type: string
        description:
          type: string
        cover_image_url:
          type: string
          format: uri
        is_public:
          type: boolean
          default: true
        expires_at:
          type: string
          format: date-time

    CollectionWithProducts:
      allOf:
        - $ref: '#/components/schemas/Collection'
        - type: object
          properties:
            products:
              type: array
              items:
                $ref: '#/components/schemas/CollectionProduct'

    CollectionProduct:
      type: object
      properties:
        collection_id:
          type: string
          format: uuid
        product_id:
          type: string
          format: uuid
        display_order:
          type: integer
        is_featured:
          type: boolean
        added_at:
          type: string
          format: date-time
        product:
          $ref: '#/components/schemas/Product'

    Customer:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        email:
          type: string
          format: email
        phone:
          type: string
        alternate_phone:
          type: string
        address:
          type: object
          properties:
            street:
              type: string
            city:
              type: string
            state:
              type: string
            country:
              type: string
            pincode:
              type: string
        total_orders:
          type: integer
        total_spent:
          type: number
          format: float
        last_order_date:
          type: string
          format: date-time
        preferred_method:
          type: string
          enum: [phone, email, whatsapp]
        notes:
          type: string
        is_active:
          type: boolean
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    CreateCustomerRequest:
      type: object
      required:
        - name
        - phone
      properties:
        name:
          type: string
        email:
          type: string
          format: email
        phone:
          type: string
        alternate_phone:
          type: string
        address:
          type: object
          properties:
            street:
              type: string
            city:
              type: string
            state:
              type: string
            country:
              type: string
            pincode:
              type: string
        preferred_method:
          type: string
          enum: [phone, email, whatsapp]
          default: phone
        notes:
          type: string

    Order:
      type: object
      properties:
        id:
          type: string
          format: uuid
        order_number:
          type: string
        customer_id:
          type: string
          format: uuid
        status:
          type: string
          enum: [pending, confirmed, processing, shipped, delivered, cancelled]
        total_amount:
          type: number
          format: float
        discount_amount:
          type: number
          format: float
        final_amount:
          type: number
          format: float
        delivery_address:
          type: object
          properties:
            street:
              type: string
            city:
              type: string
            state:
              type: string
            country:
              type: string
            pincode:
              type: string
        delivery_date:
          type: string
          format: date-time
        notes:
          type: string
        internal_notes:
          type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        customer:
          $ref: '#/components/schemas/Customer'
        items:
          type: array
          items:
            $ref: '#/components/schemas/OrderItem'

    OrderItem:
      type: object
      properties:
        id:
          type: string
          format: uuid
        order_id:
          type: string
          format: uuid
        product_id:
          type: string
          format: uuid
        quantity:
          type: integer
        unit_price:
          type: number
          format: float
        total_price:
          type: number
          format: float
        product:
          $ref: '#/components/schemas/Product'

    CreateOrderRequest:
      type: object
      required:
        - customer_id
        - items
        - delivery_address
      properties:
        customer_id:
          type: string
          format: uuid
        items:
          type: array
          minItems: 1
          items:
            type: object
            required:
              - product_id
              - quantity
            properties:
              product_id:
                type: string
                format: uuid
              quantity:
                type: integer
                minimum: 1
        delivery_address:
          type: object
          required:
            - street
            - city
            - state
            - country
          properties:
            street:
              type: string
            city:
              type: string
            state:
              type: string
            country:
              type: string
            pincode:
              type: string
        discount_amount:
          type: number
          format: float
          minimum: 0
          default: 0
        delivery_date:
          type: string
          format: date-time
        notes:
          type: string

    InventoryStatus:
      type: object
      properties:
        product_id:
          type: string
          format: uuid
        product_name:
          type: string
        product_sku:
          type: string
        current_stock:
          type: integer
        min_stock_level:
          type: integer
        is_low_stock:
          type: boolean
        is_out_of_stock:
          type: boolean
        last_updated:
          type: string
          format: date-time
        product:
          $ref: '#/components/schemas/Product'

    InventoryLog:
      type: object
      properties:
        id:
          type: string
          format: uuid
        product_id:
          type: string
          format: uuid
        change_type:
          type: string
          enum: [increase, decrease, adjust]
        quantity_change:
          type: integer
        previous_stock:
          type: integer
        new_stock:
          type: integer
        reason:
          type: string
        created_at:
          type: string
          format: date-time
        product:
          $ref: '#/components/schemas/Product'

    UpdateInventoryRequest:
      type: object
      required:
        - quantity
        - change_type
        - reason
      properties:
        quantity:
          type: integer
          minimum: 0
        change_type:
          type: string
          enum: [increase, decrease, adjust]
        reason:
          type: string
          minLength: 1
          maxLength: 255

    CreateCollectionRequest:
      type: object
      required:
        - slug
        - name
      properties:
        slug:
          type: string
          pattern: '^[a-z0-9-]+$'
        name:
          type: string
        description:
          type: string
        cover_image_url:
          type: string
          format: uri
        is_public:
          type: boolean
          default: true
        expires_at:
          type: string
          format: date-time

    CollectionWithProducts:
      allOf:
        - $ref: '#/components/schemas/Collection'
        - type: object
          properties:
            products:
              type: array
              items:
                $ref: '#/components/schemas/CollectionProduct'

    CollectionProduct:
      type: object
      properties:
        collection_id:
          type: string
          format: uuid
        product_id:
          type: string
          format: uuid
        display_order:
          type: integer
        is_featured:
          type: boolean
        added_at:
          type: string
          format: date-time
        product:
          $ref: '#/components/schemas/Product'

    Customer:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        email:
          type: string
          format: email
        phone:
          type: string
        alternate_phone:
          type: string
        address:
          type: object
          properties:
            street:
              type: string
            city:
              type: string
            state:
              type: string
            country:
              type: string
            pincode:
              type: string
        total_orders:
          type: integer
        total_spent:
          type: number
          format: float
        last_order_date:
          type: string
          format: date-time
        preferred_method:
          type: string
          enum: [phone, email, whatsapp]
        notes:
          type: string
        is_active:
          type: boolean
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    CreateCustomerRequest:
      type: object
      required:
        - name
        - phone
      properties:
        name:
          type: string
        email:
          type: string
          format: email
        phone:
          type: string
        alternate_phone:
          type: string
        address:
          type: object
          properties:
            street:
              type: string
            city:
              type: string
            state:
              type: string
            country:
              type: string
            pincode:
              type: string
        preferred_method:
          type: string
          enum: [phone, email, whatsapp]
          default: phone
        notes:
          type: string

    Order:
      type: object
      properties:
        id:
          type: string
          format: uuid
        order_number:
          type: string
        customer_id:
          type: string
          format: uuid
        status:
          type: string
          enum: [pending, confirmed, processing, shipped, delivered, cancelled]
        total_amount:
          type: number
          format: float
        discount_amount:
          type: number
          format: float
        final_amount:
          type: number
          format: float
        delivery_address:
          type: object
          properties:
            street:
              type: string
            city:
              type: string
            state:
              type: string
            country:
              type: string
            pincode:
              type: string
        delivery_date:
          type: string
          format: date-time
        notes:
          type: string
        internal_notes:
          type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        customer:
          $ref: '#/components/schemas/Customer'
        items:
          type: array
          items:
            $ref: '#/components/schemas/OrderItem'

    OrderItem:
      type: object
      properties:
        id:
          type: string
          format: uuid
        order_id:
          type: string
          format: uuid
        product_id:
          type: string
          format: uuid
        quantity:
          type: integer
        unit_price:
          type: number
          format: float
        total_price:
          type: number
          format: float
        product:
          $ref: '#/components/schemas/Product'

    CreateOrderRequest:
      type: object
      required:
        - customer_id
        - items
        - delivery_address
      properties:
        customer_id:
          type: string
          format: uuid
        items:
          type: array
          minItems: 1
          items:
            type: object
            required:
              - product_id
              - quantity
            properties:
              product_id:
                type: string
                format: uuid
              quantity:
                type: integer
                minimum: 1
        delivery_address:
          type: object
          required:
            - street
            - city
            - state
            - country
          properties:
            street:
              type: string
            city:
              type: string
            state:
              type: string
            country:
              type: string
            pincode:
              type: string
        discount_amount:
          type: number
          format: float
          minimum: 0
          default: 0
        delivery_date:
          type: string
          format: date-time
        notes:
          type: string

    InventoryStatus:
      type: object
      properties:
        product_id:
          type: string
          format: uuid
        product_name:
          type: string
        product_sku:
          type: string
        current_stock:
          type: integer
        min_stock_level:
          type: integer
        is_low_stock:
          type: boolean
        is_out_of_stock:
          type: boolean
        last_updated:
          type: string
          format: date-time
        product:
          $ref: '#/components/schemas/Product'

    InventoryLog:
      type: object
      properties:
        id:
          type: string
          format: uuid
        product_id:
          type: string
          format: uuid
        change_type:
          type: string
          enum: [increase, decrease, adjust]
        quantity_change:
          type: integer
        previous_stock:
          type: integer
        new_stock:
          type: integer
        reason:
          type: string
        created_at:
          type: string
          format: date-time
        product:
          $ref: '#/components/schemas/Product'

    UpdateInventoryRequest:
      type: object
      required:
        - quantity
        - change_type
        - reason
      properties:
        quantity:
          type: integer
          minimum: 0
        change_type:
          type: string
          enum: [increase, decrease, adjust]
        reason:
          type: string
          minLength: 1
          maxLength: 255

    ErrorResponse:
      type: object
      properties:
        error:
          type: string
        message:
          type: string
        code:
          type: integer
        details:
          type: string

  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from Google OAuth authentication
    GoogleOAuth:
      type: oauth2
      description: Google OAuth 2.0 authentication
      flows:
        authorizationCode:
          authorizationUrl: https://accounts.google.com/o/oauth2/auth
          tokenUrl: https://oauth2.googleapis.com/token
          scopes:
            openid: OpenID Connect
            email: Access to email address
            profile: Access to basic profile information

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "invalid_request"
            message: "Invalid request parameters"
            code: 400
            details: "Validation failed for field 'price': must be greater than 0"
    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "not_found"
            message: "Resource not found"
            code: 404
            details: "Product with ID '123e4567-e89b-12d3-a456-************' not found"
    InternalServerError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
          example:
            error: "internal_error"
            message: "Internal server error"
            code: 500
            details: "Database connection failed"

  examples:
    ProductExample:
      summary: Example jewelry product
      value:
        id: "21c09fe8-453e-4a09-86bb-a42bd7589ffd"
        sku: "GR001"
        name: "Classic Gold Ring"
        description: "Elegant 18K gold ring with traditional design"
        price: 45000.00
        cost_price: 35000.00
        category: "rings"
        subcategory: "gold_rings"
        weight: 5.2
        material: "Gold"
        metal_purity: "18K"
        dimensions:
          length: 20
          width: 20
          height: 8
          unit: "mm"
        availability: "available"
        stock_quantity: 10
        min_stock_level: 2
        tags: ["gold", "ring", "traditional", "18k"]
        is_featured: true
        is_active: true
        created_at: "2025-06-29T06:35:04.469729Z"
        updated_at: "2025-06-29T06:35:04.469729Z"

    CollectionExample:
      summary: Example jewelry collection
      value:
        id: "31140512-a48c-4e6f-84e7-44bc020c240b"
        slug: "bridal-collection-2024"
        name: "Bridal Collection 2024"
        description: "Exquisite bridal jewelry collection featuring traditional and contemporary designs"
        cover_image_url: "https://example.com/images/bridal-collection-cover.jpg"
        is_public: true
        is_active: true
        expires_at: null
        view_count: 1250
        share_count: 45
        product_count: 6
        created_at: "2025-06-29T06:35:04.469729Z"
        updated_at: "2025-06-29T06:35:04.469729Z"

    OrderExample:
      summary: Example order
      value:
        id: "07f123eb-b33d-4b04-ad4b-31ee42747fde"
        order_number: "*********"
        customer_id: "c1234567-e89b-12d3-a456-************"
        status: "confirmed"
        total_amount: 25000.00
        discount_amount: 0.00
        final_amount: 25000.00
        delivery_address:
          street: "123 MG Road"
          city: "Mumbai"
          state: "Maharashtra"
          country: "India"
          pincode: "400001"
        delivery_date: null
        notes: "Please handle with care"
        internal_notes: "Customer prefers morning delivery"
        created_at: "2025-06-29T06:35:04.475117Z"
        updated_at: "2025-06-29T06:35:04.475117Z"

# Global security (will be overridden when authentication is implemented)
security: []
