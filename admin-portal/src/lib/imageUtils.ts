import type { ProductImage } from '../types';

/**
 * Image size variants available from the backend
 */
export type ImageSize = 'thumbnail' | 'small' | 'medium' | 'large' | 'original';

/**
 * Image size dimensions for reference
 */
export const IMAGE_DIMENSIONS = {
  thumbnail: { width: 150, height: 150 },
  small: { width: 300, height: 300 },
  medium: { width: 600, height: 600 },
  large: { width: 1200, height: 1200 },
  original: { width: null, height: null }, // Full size
} as const;

/**
 * Use cases and their recommended image sizes
 */
export const IMAGE_USE_CASES = {
  // List views and small previews
  LIST_THUMBNAIL: 'thumbnail' as ImageSize,
  CARD_SMALL: 'small' as ImageSize,
  
  // Medium displays
  CARD_MEDIUM: 'medium' as ImageSize,
  FORM_PREVIEW: 'medium' as ImageSize,
  
  // Large displays
  GALLERY_MAIN: 'large' as ImageSize,
  DETAIL_VIEW: 'large' as ImageSize,
  
  // Full size
  ZOOM_VIEW: 'original' as ImageSize,
  DOWNLOAD: 'original' as ImageSize,
} as const;

/**
 * Generate optimized image URL with size parameter
 */
export function getOptimizedImageUrl(
  baseImageUrl: string,
  size: ImageSize = 'medium',
  quality?: number
): string {
  if (!baseImageUrl) return '';
  
  // Check if it's already a proxy URL
  const isProxyUrl = baseImageUrl.includes('/api/v1/proxy/images/');
  
  if (isProxyUrl) {
    // Add or update size parameter
    const url = new URL(baseImageUrl, window.location.origin);
    url.searchParams.set('size', size);
    if (quality) {
      url.searchParams.set('quality', quality.toString());
    }
    return url.toString();
  }
  
  // For non-proxy URLs, return as-is (fallback)
  return baseImageUrl;
}

/**
 * Generate optimized image URL from ProductImage object
 */
export function getOptimizedProductImageUrl(
  image: ProductImage,
  size: ImageSize = 'medium',
  quality?: number
): string {
  return getOptimizedImageUrl(image.image_url, size, quality);
}

/**
 * Get the best image size for a given display context
 */
export function getBestImageSize(context: keyof typeof IMAGE_USE_CASES): ImageSize {
  return IMAGE_USE_CASES[context];
}

/**
 * Generate responsive image URLs for different screen sizes
 */
export function getResponsiveImageUrls(baseImageUrl: string) {
  return {
    thumbnail: getOptimizedImageUrl(baseImageUrl, 'thumbnail'),
    small: getOptimizedImageUrl(baseImageUrl, 'small'),
    medium: getOptimizedImageUrl(baseImageUrl, 'medium'),
    large: getOptimizedImageUrl(baseImageUrl, 'large'),
    original: getOptimizedImageUrl(baseImageUrl, 'original'),
  };
}

/**
 * Generate srcSet string for responsive images
 */
export function generateSrcSet(baseImageUrl: string): string {
  const urls = getResponsiveImageUrls(baseImageUrl);
  return [
    `${urls.small} 300w`,
    `${urls.medium} 600w`,
    `${urls.large} 1200w`,
  ].join(', ');
}

/**
 * Generate sizes attribute for responsive images
 */
export function generateSizesAttribute(breakpoints?: {
  mobile?: string;
  tablet?: string;
  desktop?: string;
}): string {
  const defaultBreakpoints = {
    mobile: '100vw',
    tablet: '50vw',
    desktop: '33vw',
    ...breakpoints,
  };
  
  return [
    `(max-width: 640px) ${defaultBreakpoints.mobile}`,
    `(max-width: 1024px) ${defaultBreakpoints.tablet}`,
    defaultBreakpoints.desktop,
  ].join(', ');
}

/**
 * Get placeholder image URL
 */
export function getPlaceholderImageUrl(width: number = 300, height: number = 300): string {
  const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:8080/api/v1';
  return `${apiUrl}/images/placeholder?width=${width}&height=${height}`;
}

/**
 * Preload critical images
 */
export function preloadImage(url: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = reject;
    img.src = url;
  });
}

/**
 * Lazy load image with intersection observer
 */
export function lazyLoadImage(
  img: HTMLImageElement,
  src: string,
  options?: IntersectionObserverInit
): void {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        img.src = src;
        img.classList.remove('lazy');
        observer.unobserve(img);
      }
    });
  }, options);
  
  observer.observe(img);
}
