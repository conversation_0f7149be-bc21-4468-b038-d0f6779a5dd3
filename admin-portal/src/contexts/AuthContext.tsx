import React, { createContext, useContext, useReducer, useEffect, type ReactNode } from 'react';
import type { AuthState, AuthContextType, User, AuthError } from '../types/auth';

// Initial state
const initialState: AuthState = {
  user: null,
  token: localStorage.getItem('jwt_token'),
  isAuthenticated: false,
  isLoading: true,
  error: null,
};

// Action types
type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: { user: User; token: string } }
  | { type: 'AUTH_ERROR'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'CLEAR_ERROR' }
  | { type: 'SET_LOADING'; payload: boolean };

// Reducer
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case 'AUTH_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    case 'AUTH_ERROR':
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
      };
    case 'LOGOUT':
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    case 'SET_LOADING':
      return {
        ...state,
        isLoading: action.payload,
      };
    default:
      return state;
  }
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Auth provider component
interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // API base URL
  const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8080/api/v1';

  // Login function - redirects to Google OAuth
  const login = (redirectTo?: string) => {
    const redirectUrl = redirectTo || window.location.pathname + window.location.search;
    const encodedRedirect = encodeURIComponent(redirectUrl);
    window.location.href = `${API_BASE_URL}/auth/google/login?redirect=${encodedRedirect}`;
  };

  // Logout function
  const logout = () => {
    localStorage.removeItem('jwt_token');
    dispatch({ type: 'LOGOUT' });
  };

  // Check authentication status
  const checkAuth = async () => {
    const token = localStorage.getItem('jwt_token');
    
    if (!token) {
      dispatch({ type: 'AUTH_ERROR', payload: 'No authentication token found' });
      return;
    }

    try {
      dispatch({ type: 'AUTH_START' });

      const response = await fetch(`${API_BASE_URL}/auth/me`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          localStorage.removeItem('jwt_token');
          throw new Error('Authentication expired. Please login again.');
        }
        
        const errorData: AuthError = await response.json().catch(() => ({
          error: 'unknown_error',
          message: 'An unknown error occurred',
          code: response.status,
        }));
        
        throw new Error(errorData.message || 'Authentication failed');
      }

      const data = await response.json();
      
      if (data.user) {
        dispatch({
          type: 'AUTH_SUCCESS',
          payload: {
            user: data.user,
            token,
          },
        });
      } else {
        throw new Error('Invalid response from server');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Authentication failed';
      dispatch({ type: 'AUTH_ERROR', payload: errorMessage });
      
      // Remove invalid token
      if (errorMessage.includes('expired') || errorMessage.includes('invalid')) {
        localStorage.removeItem('jwt_token');
      }
    }
  };

  // Clear error function
  const clearError = () => {
    dispatch({ type: 'CLEAR_ERROR' });
  };

  // Check for token in URL (from OAuth callback)
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');
    const error = urlParams.get('error');
    const redirectTo = urlParams.get('redirect');

    if (token) {
      localStorage.setItem('jwt_token', token);

      // Store redirect URL for after authentication
      if (redirectTo) {
        localStorage.setItem('auth_redirect', decodeURIComponent(redirectTo));
      }

      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
      // Check auth with new token
      checkAuth();
    } else if (error) {
      dispatch({ type: 'AUTH_ERROR', payload: decodeURIComponent(error) });
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
    } else {
      // Check existing token
      checkAuth();
    }
  }, []);

  // Handle redirect after successful authentication
  useEffect(() => {
    if (state.isAuthenticated && !state.isLoading) {
      const redirectUrl = localStorage.getItem('auth_redirect');
      if (redirectUrl && redirectUrl !== '/login' && redirectUrl !== '/unauthorized') {
        localStorage.removeItem('auth_redirect');
        // Use setTimeout to avoid navigation during render
        setTimeout(() => {
          window.location.href = redirectUrl;
        }, 100);
      }
    }
  }, [state.isAuthenticated, state.isLoading]);

  const contextValue: AuthContextType = {
    ...state,
    login,
    logout,
    checkAuth,
    clearError,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
