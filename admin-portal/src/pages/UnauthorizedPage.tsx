import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { ExclamationTriangleIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';

const UnauthorizedPage: React.FC = () => {
  const { logout, user } = useAuth();

  const handleGoBack = () => {
    logout();
  };

  const handleContactAdmin = () => {
    const subject = encodeURIComponent('Admin Portal Access Request');
    const body = encodeURIComponent(
      `Hello,\n\nI am requesting access to the Anand Jewels Admin Portal.\n\nMy email address: ${user?.email || 'N/A'}\nMy name: ${user?.name || 'N/A'}\n\nPlease add me to the authorized users list.\n\nThank you.`
    );
    window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <img
            className="mx-auto h-20 w-auto"
            src="/logo_small.JPG"
            alt="Anand Jewels"
          />
          <div className="mt-6">
            <ExclamationTriangleIcon className="mx-auto h-16 w-16 text-red-500" />
          </div>
          <h2 className="mt-4 text-3xl font-extrabold text-gray-900">
            Access Denied
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            You are not authorized to access the admin portal
          </p>
        </div>

        {/* Error Details */}
        <div className="bg-white py-8 px-6 shadow rounded-lg">
          <div className="space-y-6">
            {/* User Info */}
            {user && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-sm font-medium text-gray-900 mb-2">
                  Signed in as:
                </h3>
                <div className="flex items-center space-x-3">
                  {user.picture_url && (
                    <img
                      className="h-10 w-10 rounded-full"
                      src={user.picture_url}
                      alt={user.name}
                    />
                  )}
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {user.name}
                    </p>
                    <p className="text-sm text-gray-500">
                      {user.email}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Error Message */}
            <div className="text-center">
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Your email is not whitelisted
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                Only authorized email addresses can access the Anand Jewels Admin Portal. 
                Your account has been authenticated successfully, but you don't have 
                permission to access administrative functions.
              </p>
            </div>

            {/* Actions */}
            <div className="space-y-3">
              <button
                onClick={handleContactAdmin}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
              >
                Request Access
              </button>
              
              <button
                onClick={handleGoBack}
                className="w-full flex justify-center items-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
              >
                <ArrowLeftIcon className="h-4 w-4 mr-2" />
                Sign Out & Go Back
              </button>
            </div>

            {/* Help Text */}
            <div className="text-center">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-300" />
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-white text-gray-500">
                    Need Help?
                  </span>
                </div>
              </div>
              
              <div className="mt-4 text-sm text-gray-600">
                <p>If you believe this is an error, please contact:</p>
                <p className="mt-1 font-medium"><EMAIL></p>
                <p className="mt-2 text-xs">
                  Include your email address and the reason you need access.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center text-sm text-gray-500">
          <p>© 2024 Anand Jewels. All rights reserved.</p>
        </div>
      </div>
    </div>
  );
};

export default UnauthorizedPage;
