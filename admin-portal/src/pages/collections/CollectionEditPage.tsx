import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { ArrowLeftIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { getCollection, updateCollection, addProductToCollection, removeProductFromCollection } from '../../lib/api';
import CollectionForm from '../../components/collections/CollectionForm';
import type { UpdateCollectionRequest } from '../../types';

const CollectionEditPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const queryClient = useQueryClient();
  const [updateError, setUpdateError] = useState<string | null>(null);

  const { data: collection, isLoading, error } = useQuery({
    queryKey: ['collection', id],
    queryFn: () => getCollection(id!),
    enabled: !!id,
  });

  const updateMutation = useMutation({
    mutationFn: (data: UpdateCollectionRequest) => updateCollection(id!, data),
    onSuccess: (data) => {
      setUpdateError(null);
      // Invalidate collections cache
      queryClient.invalidateQueries({ queryKey: ['collections'] });
      queryClient.invalidateQueries({ queryKey: ['collection', id] });

      navigate('/collections', {
        state: { message: `Collection "${data.name}" updated successfully!` }
      });
    },
    onError: (error: any) => {
      console.error('Failed to update collection:', error);

      // Extract detailed error message
      let errorMessage = 'Failed to update collection';
      if (error?.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error?.response?.data?.details) {
        errorMessage = error.response.data.details;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      setUpdateError(errorMessage);
    },
  });

  const handleSubmit = async (data: UpdateCollectionRequest, selectedProducts?: string[]) => {
    try {
      setUpdateError(null);

      // Update the collection first
      await updateMutation.mutateAsync(data);

      // Handle product updates if provided
      if (selectedProducts && collection) {
        const currentProductIds = collection.products?.map(p => p.id) || [];
        const newProductIds = selectedProducts;

        // Find products to add and remove
        const productsToAdd = newProductIds.filter(id => !currentProductIds.includes(id));
        const productsToRemove = currentProductIds.filter(id => !newProductIds.includes(id));

        try {
          // Add new products
          await Promise.all(
            productsToAdd.map((productId, index) =>
              addProductToCollection(collection.id, productId, currentProductIds.length + index)
            )
          );

          // Remove deselected products
          await Promise.all(
            productsToRemove.map(productId =>
              removeProductFromCollection(collection.id, productId)
            )
          );
        } catch (productError: any) {
          console.error('Failed to update collection products:', productError);
          let errorMessage = 'Failed to update collection products';
          if (productError?.response?.data?.message) {
            errorMessage = productError.response.data.message;
          } else if (productError?.message) {
            errorMessage = productError.message;
          }
          setUpdateError(errorMessage);
        }
      }
    } catch (error: any) {
      console.error('Failed to update collection:', error);
      // Error is already handled by the mutation onError
    }
  };

  const handleCancel = () => {
    navigate('/collections');
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/collections')}
            className="text-gray-400 hover:text-gray-500"
          >
            <ArrowLeftIcon className="h-6 w-6" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Edit Collection</h1>
          </div>
        </div>
        <div className="card p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-2 text-gray-500">Loading collection...</p>
        </div>
      </div>
    );
  }

  if (error || !collection) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/collections')}
            className="text-gray-400 hover:text-gray-500"
          >
            <ArrowLeftIcon className="h-6 w-6" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Edit Collection</h1>
          </div>
        </div>
        <div className="card p-8 text-center">
          <div className="text-red-600 mb-4">
            {error ? 'Error loading collection' : 'Collection not found'}
          </div>
          <button onClick={() => navigate('/collections')} className="btn-primary">
            Back to Collections
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => navigate('/collections')}
          className="text-gray-400 hover:text-gray-500"
        >
          <ArrowLeftIcon className="h-6 w-6" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Edit Collection</h1>
          <p className="mt-1 text-sm text-gray-500">
            Update "{collection.name}" details
          </p>
        </div>
      </div>

      {/* Error display */}
      {updateError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <ExclamationTriangleIcon className="h-5 w-5 text-red-400 mr-3 mt-0.5" />
            <div>
              <h3 className="text-sm font-medium text-red-800">Update Failed</h3>
              <p className="text-sm text-red-700 mt-1">{updateError}</p>
            </div>
          </div>
        </div>
      )}

      {/* Collection form */}
      <div className="card p-6">
        <CollectionForm
          initialData={collection}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={updateMutation.isPending}
          error={updateMutation.error}
        />
      </div>
    </div>
  );
};

export default CollectionEditPage;
