import React from 'react';
import { useNavi<PERSON>, use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { 
  ArrowLeftIcon, 
  PencilIcon, 
  CalendarIcon,
  EyeIcon,
  GlobeAltIcon,
  LockClosedIcon,
  DocumentDuplicateIcon,
  PhotoIcon,
  TagIcon,
  CubeIcon
} from '@heroicons/react/24/outline';
import { getCollection } from '../../lib/api';
import { formatDate } from '../../lib/utils';
import { getOptimizedImageUrl, getBestImageSize } from '../../lib/imageUtils';
import ProductImageDisplay from '../../components/products/ProductImageDisplay';

const CollectionDetailPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  const { data: collection, isLoading, error } = useQuery({
    queryKey: ['collection', id],
    queryFn: () => getCollection(id!),
    enabled: !!id,
  });

  const handleCopyUrl = () => {
    if (collection) {
      const url = `${window.location.origin}/collection/${collection.slug}`;
      navigator.clipboard.writeText(url);
      // You could add a toast notification here
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/collections')}
            className="text-gray-400 hover:text-gray-500"
          >
            <ArrowLeftIcon className="h-6 w-6" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Collection Details</h1>
          </div>
        </div>
        <div className="card p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading collection details...</p>
        </div>
      </div>
    );
  }

  if (error || !collection) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/collections')}
            className="text-gray-400 hover:text-gray-500"
          >
            <ArrowLeftIcon className="h-6 w-6" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Collection Details</h1>
          </div>
        </div>
        <div className="card p-8 text-center">
          <div className="text-red-600 mb-4">
            {error ? 'Error loading collection' : 'Collection not found'}
          </div>
          <button onClick={() => navigate('/collections')} className="btn-primary">
            Back to Collections
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/collections')}
            className="text-gray-400 hover:text-gray-500"
          >
            <ArrowLeftIcon className="h-6 w-6" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Collection Details</h1>
            <p className="mt-1 text-sm text-gray-500">
              View collection information and products
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={handleCopyUrl}
            className="btn-outline"
            title="Copy collection URL"
          >
            <DocumentDuplicateIcon className="h-4 w-4 mr-2" />
            Copy URL
          </button>
          <Link
            to={`/collections/${collection.id}/edit`}
            className="btn-secondary"
          >
            <PencilIcon className="h-4 w-4 mr-2" />
            Edit Collection
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Collection Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <div className="card p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-medium text-gray-900">Basic Information</h2>
              <div className="flex items-center space-x-2">
                {collection.is_public ? (
                  <span className="badge badge-green">
                    <GlobeAltIcon className="h-3 w-3 mr-1" />
                    Public
                  </span>
                ) : (
                  <span className="badge badge-gray">
                    <LockClosedIcon className="h-3 w-3 mr-1" />
                    Private
                  </span>
                )}
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center">
                  <TagIcon className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Collection Name</div>
                    <div className="text-sm text-gray-600">{collection.name}</div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <CubeIcon className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Slug</div>
                    <div className="text-sm text-gray-600">{collection.slug}</div>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center">
                  <CalendarIcon className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Created</div>
                    <div className="text-sm text-gray-600">{formatDate(collection.created_at)}</div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <EyeIcon className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Views</div>
                    <div className="text-sm text-gray-600">{collection.view_count || 0}</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Description */}
            {collection.description && (
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="text-sm font-medium text-gray-900 mb-2">Description</div>
                <div className="text-sm text-gray-600">{collection.description}</div>
              </div>
            )}
          </div>

          {/* Cover Image */}
          {collection.cover_image_url && (
            <div className="card p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Cover Image</h2>
              <div className="aspect-video overflow-hidden rounded-lg bg-gray-100">
                <img
                  src={getOptimizedImageUrl(collection.cover_image_url, getBestImageSize('DETAIL_VIEW'))}
                  alt={collection.name}
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          )}

          {/* Products */}
          <div className="card p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Products</h2>
            {collection.products && collection.products.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {collection.products.map((product) => (
                  <div key={product.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <div className="flex items-center space-x-3">
                      <ProductImageDisplay
                        images={product.images}
                        productName={product.name}
                        size="sm"
                        showGalleryButton={false}
                      />
                      <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium text-gray-900 truncate">
                          {product.name}
                        </div>
                        <div className="text-xs text-gray-500">
                          SKU: {product.sku}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <PhotoIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <div className="text-sm text-gray-500">No products in this collection</div>
              </div>
            )}
          </div>
        </div>

        {/* Statistics Sidebar */}
        <div className="space-y-6">
          {/* Collection Statistics */}
          <div className="card p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Statistics</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900">Total Products</span>
                <span className="text-sm font-bold text-gray-900">
                  {collection.products?.length || 0}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900">Total Views</span>
                <span className="text-sm font-bold text-gray-900">
                  {collection.view_count || 0}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900">Status</span>
                <span className={`text-sm font-bold ${collection.is_public ? 'text-green-600' : 'text-gray-600'}`}>
                  {collection.is_public ? 'Public' : 'Private'}
                </span>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="card p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
            <div className="space-y-3">
              <button
                onClick={handleCopyUrl}
                className="block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
              >
                Copy Collection URL
              </button>
              <Link
                to={`/collections/${collection.id}/edit`}
                className="block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
              >
                Edit Collection
              </Link>
              {collection.is_public && (
                <a
                  href={`${window.location.origin}/collection/${collection.slug}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
                >
                  View Public Page
                </a>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CollectionDetailPage;
