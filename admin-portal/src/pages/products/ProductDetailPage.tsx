import React from 'react';
import { useNavigate, use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { 
  ArrowLeftIcon, 
  PencilIcon, 
  TagIcon,
  CubeIcon,
  ScaleIcon,
  SparklesIcon,
  CalendarIcon,
  EyeIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { getProduct } from '../../lib/api';
import { formatDate, formatWeight } from '../../lib/utils';
import ProductImageDisplay from '../../components/products/ProductImageDisplay';

const ProductDetailPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  const { data: product, isLoading, error } = useQuery({
    queryKey: ['product', id],
    queryFn: () => getProduct(id!),
    enabled: !!id,
  });

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/products')}
            className="text-gray-400 hover:text-gray-500"
          >
            <ArrowLeftIcon className="h-6 w-6" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Product Details</h1>
          </div>
        </div>
        <div className="card p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading product details...</p>
        </div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/products')}
            className="text-gray-400 hover:text-gray-500"
          >
            <ArrowLeftIcon className="h-6 w-6" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Product Details</h1>
          </div>
        </div>
        <div className="card p-8 text-center">
          <div className="text-red-600 mb-4">
            {error ? 'Error loading product' : 'Product not found'}
          </div>
          <button onClick={() => navigate('/products')} className="btn-primary">
            Back to Products
          </button>
        </div>
      </div>
    );
  }

  const getAvailabilityBadge = () => {
    switch (product.availability) {
      case 'available':
        return { label: 'Available', class: 'badge-green', icon: CheckCircleIcon };
      case 'out_of_stock':
        return { label: 'Out of Stock', class: 'badge-red', icon: XCircleIcon };
      case 'discontinued':
        return { label: 'Discontinued', class: 'badge-gray', icon: ExclamationTriangleIcon };
      default:
        return { label: 'Unknown', class: 'badge-gray', icon: ExclamationTriangleIcon };
    }
  };

  const availability = getAvailabilityBadge();

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/products')}
            className="text-gray-400 hover:text-gray-500"
          >
            <ArrowLeftIcon className="h-6 w-6" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Product Details</h1>
            <p className="mt-1 text-sm text-gray-500">
              View product information and specifications
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Link
            to={`/products/${product.id}/edit`}
            className="btn-secondary"
          >
            <PencilIcon className="h-4 w-4 mr-2" />
            Edit Product
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Product Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <div className="card p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-medium text-gray-900">Basic Information</h2>
              <div className="flex items-center space-x-2">
                <span className={`badge ${availability.class}`}>
                  <availability.icon className="h-3 w-3 mr-1" />
                  {availability.label}
                </span>
                {product.is_featured && (
                  <span className="badge badge-gold">
                    <SparklesIcon className="h-3 w-3 mr-1" />
                    Featured
                  </span>
                )}
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center">
                  <TagIcon className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Product Name</div>
                    <div className="text-sm text-gray-600">{product.name}</div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <CubeIcon className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">SKU</div>
                    <div className="text-sm text-gray-600">{product.sku}</div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <TagIcon className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Category</div>
                    <div className="text-sm text-gray-600">
                      {product.category}
                      {product.subcategory && ` > ${product.subcategory}`}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center">
                  <ScaleIcon className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Gross Weight</div>
                    <div className="text-sm text-gray-600">
                      {product.weight ? formatWeight(product.weight) : 'N/A'}
                    </div>
                  </div>
                </div>
                
                {product.net_weight && (
                  <div className="flex items-center">
                    <ScaleIcon className="h-5 w-5 text-gray-400 mr-3" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">Net Weight</div>
                      <div className="text-sm text-gray-600">{formatWeight(product.net_weight)}</div>
                    </div>
                  </div>
                )}
                
                <div className="flex items-center">
                  <CalendarIcon className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Created</div>
                    <div className="text-sm text-gray-600">{formatDate(product.created_at)}</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Description */}
            {product.description && (
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="text-sm font-medium text-gray-900 mb-2">Description</div>
                <div className="text-sm text-gray-600">{product.description}</div>
              </div>
            )}

            {/* Material & Specifications */}
            {(product.material || product.metal_purity) && (
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="text-sm font-medium text-gray-900 mb-4">Material & Specifications</div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {product.material && (
                    <div>
                      <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">Material</div>
                      <div className="text-sm text-gray-900">{product.material}</div>
                    </div>
                  )}
                  {product.metal_purity && (
                    <div>
                      <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">Metal Purity</div>
                      <div className="text-sm text-gray-900">{product.metal_purity}</div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Tags */}
            {product.tags && product.tags.length > 0 && (
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="text-sm font-medium text-gray-900 mb-2">Tags</div>
                <div className="flex flex-wrap gap-2">
                  {product.tags.map((tag, index) => (
                    <span key={index} className="badge badge-info">
                      {tag}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Product Images */}
          <div className="card p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Product Images</h2>
            {product.images && product.images.length > 0 ? (
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {product.images.map((image) => (
                  <div key={image.id} className="relative">
                    <ProductImageDisplay
                      images={[image]}
                      productName={product.name}
                      size="md"
                      showHoverPreview={true}
                    />
                    {image.is_primary && (
                      <div className="absolute top-2 left-2">
                        <span className="badge badge-primary text-xs">Primary</span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <EyeIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <div className="text-sm text-gray-500">No images available</div>
              </div>
            )}
          </div>
        </div>

        {/* Statistics Sidebar */}
        <div className="space-y-6">
          {/* Inventory Information */}
          <div className="card p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Inventory</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900">Stock Quantity</span>
                <span className="text-sm font-bold text-gray-900">{product.stock_quantity}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900">Min Stock Level</span>
                <span className="text-sm font-bold text-gray-900">{product.min_stock_level}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900">Availability</span>
                <span className={`text-sm font-bold ${availability.class.includes('green') ? 'text-green-600' : availability.class.includes('red') ? 'text-red-600' : 'text-gray-600'}`}>
                  {availability.label}
                </span>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="card p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
            <div className="space-y-3">
              <Link
                to={`/products/${product.id}/edit`}
                className="block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
              >
                Edit Product
              </Link>
              <Link
                to={`/inventory`}
                className="block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
              >
                Manage Inventory
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailPage;
