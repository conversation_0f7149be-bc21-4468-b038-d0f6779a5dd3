import React from 'react';
import { useNavigate, use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { 
  ArrowLeftIcon, 
  PencilIcon, 
  PhoneIcon, 
  EnvelopeIcon,
  MapPinIcon,
  CalendarIcon,
  UserIcon,
  ShoppingBagIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline';
import { getCustomer } from '../../lib/api';
import { formatDate, formatWeight } from '../../lib/utils';

const CustomerDetailPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  const { data: customer, isLoading, error } = useQuery({
    queryKey: ['customer', id],
    queryFn: () => getCustomer(id!),
    enabled: !!id,
  });

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/customers')}
            className="text-gray-400 hover:text-gray-500"
          >
            <ArrowLeftIcon className="h-6 w-6" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Customer Details</h1>
          </div>
        </div>
        <div className="card p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading customer details...</p>
        </div>
      </div>
    );
  }

  if (error || !customer) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/customers')}
            className="text-gray-400 hover:text-gray-500"
          >
            <ArrowLeftIcon className="h-6 w-6" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Customer Details</h1>
          </div>
        </div>
        <div className="card p-8 text-center">
          <div className="text-red-600 mb-4">
            {error ? 'Error loading customer' : 'Customer not found'}
          </div>
          <button onClick={() => navigate('/customers')} className="btn-primary">
            Back to Customers
          </button>
        </div>
      </div>
    );
  }

  const getStatusBadge = () => {
    if (customer.total_orders === 0) {
      return { label: 'New', class: 'badge-gray' };
    } else if (customer.total_orders >= 5) {
      return { label: 'VIP', class: 'badge-gold' };
    } else {
      return { label: 'Regular', class: 'badge-green' };
    }
  };

  const status = getStatusBadge();

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/customers')}
            className="text-gray-400 hover:text-gray-500"
          >
            <ArrowLeftIcon className="h-6 w-6" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Customer Details</h1>
            <p className="mt-1 text-sm text-gray-500">
              View customer information and order history
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Link
            to={`/customers/${customer.id}/edit`}
            className="btn-secondary"
          >
            <PencilIcon className="h-4 w-4 mr-2" />
            Edit Customer
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Customer Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <div className="card p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-medium text-gray-900">Basic Information</h2>
              <span className={`badge ${status.class}`}>
                {status.label}
              </span>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center">
                  <UserIcon className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Full Name</div>
                    <div className="text-sm text-gray-600">{customer.name}</div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <PhoneIcon className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Phone</div>
                    <div className="text-sm text-gray-600">{customer.phone}</div>
                  </div>
                </div>
                
                {customer.email && (
                  <div className="flex items-center">
                    <EnvelopeIcon className="h-5 w-5 text-gray-400 mr-3" />
                    <div>
                      <div className="text-sm font-medium text-gray-900">Email</div>
                      <div className="text-sm text-gray-600">{customer.email}</div>
                    </div>
                  </div>
                )}
              </div>
              
              <div className="space-y-4">
                <div className="flex items-center">
                  <CalendarIcon className="h-5 w-5 text-gray-400 mr-3" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">Customer Since</div>
                    <div className="text-sm text-gray-600">{formatDate(customer.created_at)}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Address Information */}
          {customer.address && (
            <div className="card p-6">
              <h2 className="text-lg font-medium text-gray-900 mb-4">Address</h2>
              <div className="flex items-start">
                <MapPinIcon className="h-5 w-5 text-gray-400 mr-3 mt-0.5" />
                <div className="text-sm text-gray-600">
                  {customer.address.street && <div>{customer.address.street}</div>}
                  <div>
                    {[customer.address.city, customer.address.state, customer.address.pincode]
                      .filter(Boolean)
                      .join(', ')}
                  </div>
                  {customer.address.country && <div>{customer.address.country}</div>}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Statistics Sidebar */}
        <div className="space-y-6">
          {/* Order Statistics */}
          <div className="card p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Order Statistics</h2>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <ShoppingBagIcon className="h-5 w-5 text-gray-400 mr-3" />
                  <span className="text-sm font-medium text-gray-900">Total Orders</span>
                </div>
                <span className="text-sm font-bold text-gray-900">{customer.total_orders}</span>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <CurrencyDollarIcon className="h-5 w-5 text-gray-400 mr-3" />
                  <span className="text-sm font-medium text-gray-900">Total Spent</span>
                </div>
                <span className="text-sm font-bold text-gray-900">{formatPrice(customer.total_spent)}</span>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="card p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
            <div className="space-y-3">
              <Link
                to={`/orders?customer_id=${customer.id}`}
                className="block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
              >
                View Order History
              </Link>
              <Link
                to={`/customers/${customer.id}/edit`}
                className="block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors"
              >
                Edit Customer
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerDetailPage;
