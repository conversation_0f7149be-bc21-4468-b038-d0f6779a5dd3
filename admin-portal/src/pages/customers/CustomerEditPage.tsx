import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery, useMutation } from '@tanstack/react-query';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { getCustomer, updateCustomer } from '../../lib/api';
import CustomerForm from '../../components/customers/CustomerForm';
import type { CreateCustomerRequest } from '../../types';

const CustomerEditPage: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  const { data: customer, isLoading, error } = useQuery({
    queryKey: ['customer', id],
    queryFn: () => getCustomer(id!),
    enabled: !!id,
  });

  const updateMutation = useMutation({
    mutationFn: (data: CreateCustomerRequest) => updateCustomer(id!, data),
    onSuccess: (data) => {
      navigate('/customers', {
        state: { message: `Customer "${data.name}" updated successfully!` }
      });
    },
    onError: (error) => {
      console.error('Failed to update customer:', error);
      // You could add toast notification here
    },
  });

  const handleSubmit = (data: CreateCustomerRequest) => {
    updateMutation.mutate(data);
  };

  const handleCancel = () => {
    navigate('/customers');
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/customers')}
            className="text-gray-400 hover:text-gray-500"
          >
            <ArrowLeftIcon className="h-6 w-6" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Edit Customer</h1>
          </div>
        </div>
        <div className="card p-8 text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-2 text-gray-500">Loading customer...</p>
        </div>
      </div>
    );
  }

  if (error || !customer) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate('/customers')}
            className="text-gray-400 hover:text-gray-500"
          >
            <ArrowLeftIcon className="h-6 w-6" />
          </button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Edit Customer</h1>
          </div>
        </div>
        <div className="card p-8 text-center">
          <div className="text-red-600 mb-4">
            {error ? 'Error loading customer' : 'Customer not found'}
          </div>
          <button onClick={() => navigate('/customers')} className="btn-primary">
            Back to Customers
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => navigate('/customers')}
          className="text-gray-400 hover:text-gray-500"
        >
          <ArrowLeftIcon className="h-6 w-6" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Edit Customer</h1>
          <p className="mt-1 text-sm text-gray-500">
            Update "{customer.name}" details
          </p>
        </div>
      </div>

      {/* Customer form */}
      <div className="card p-6">
        <CustomerForm
          initialData={customer}
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={updateMutation.isPending}
          error={updateMutation.error}
        />
      </div>
    </div>
  );
};

export default CustomerEditPage;
