import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation } from '@tanstack/react-query';
import { ArrowLeftIcon } from '@heroicons/react/24/outline';
import { createCustomer } from '../../lib/api';
import CustomerForm from '../../components/customers/CustomerForm';
import type { CreateCustomerRequest } from '../../types';

const CustomerCreatePage: React.FC = () => {
  const navigate = useNavigate();

  const createMutation = useMutation({
    mutationFn: createCustomer,
    onSuccess: (data) => {
      // Navigate to the customers list or the created customer's detail page
      navigate('/customers', {
        state: { message: `Customer "${data.name}" created successfully!` }
      });
    },
    onError: (error) => {
      console.error('Failed to create customer:', error);
      // You could add toast notification here
    },
  });

  const handleSubmit = (data: CreateCustomerRequest) => {
    createMutation.mutate(data);
  };

  const handleCancel = () => {
    navigate('/customers');
  };

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center space-x-4">
        <button
          onClick={() => navigate('/customers')}
          className="text-gray-400 hover:text-gray-500"
        >
          <ArrowLeftIcon className="h-6 w-6" />
        </button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Add Customer</h1>
          <p className="mt-1 text-sm text-gray-500">
            Create a new customer profile
          </p>
        </div>
      </div>

      {/* Customer form */}
      <div className="card p-6">
        <CustomerForm
          onSubmit={handleSubmit}
          onCancel={handleCancel}
          isLoading={createMutation.isPending}
          error={createMutation.error}
        />
      </div>
    </div>
  );
};

export default CustomerCreatePage;
