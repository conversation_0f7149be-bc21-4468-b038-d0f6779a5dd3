import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import {
  PlusIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  PencilIcon,
  EyeIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  ShoppingBagIcon,
} from '@heroicons/react/24/outline';
import { getCustomers } from '../../lib/api';
import type { FilterOptions, Customer } from '../../types';
import CustomerFilters from '../../components/customers/CustomerFilters';

const CustomersPage: React.FC = () => {
  const [filters, setFilters] = useState<FilterOptions>({
    page: 1,
    limit: 20,
    search: '',
    is_active: undefined,
  });
  const [showFilters, setShowFilters] = useState(false);

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['customers', filters],
    queryFn: () => getCustomers(filters),
  });

  const handleSearch = (search: string) => {
    setFilters(prev => ({ ...prev, search, page: 1 }));
  };

  const handleFilterChange = (newFilters: Partial<FilterOptions>) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getCustomerStatus = (customer: Customer) => {
    if (!customer.is_active) {
      return { class: 'badge-danger', label: 'Inactive' };
    }
    if (customer.total_orders === 0) {
      return { class: 'badge-info', label: 'New' };
    }
    if (customer.total_orders >= 5) {
      return { class: 'badge-success', label: 'VIP' };
    }
    return { class: 'badge-success', label: 'Active' };
  };

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">Error loading customers</div>
        <button onClick={() => refetch()} className="btn-primary">
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Customers</h1>
          <p className="mt-1 text-sm text-gray-500">
            Manage customer profiles and contact information
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Link to="/customers/create" className="btn-primary">
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Customer
          </Link>
        </div>
      </div>

      {/* Search and filters */}
      <div className="card p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search customers by name, phone, or email..."
                className="form-input pl-10"
                value={filters.search}
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>
          </div>

          {/* Filter toggle */}
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="btn-outline"
          >
            <FunnelIcon className="h-4 w-4 mr-2" />
            Filters
          </button>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t">
            <CustomerFilters
              filters={filters}
              onFilterChange={handleFilterChange}
            />
          </div>
        )}
      </div>

      {/* Customers table */}
      <div className="card overflow-hidden">
        {isLoading ? (
          <div className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            <p className="mt-2 text-gray-500">Loading customers...</p>
          </div>
        ) : (
          <>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="table-header">Customer</th>
                    <th className="table-header">Contact</th>
                    <th className="table-header">Location</th>
                    <th className="table-header">Orders</th>
                    <th className="table-header">Total Spent</th>
                    <th className="table-header">Status</th>
                    <th className="table-header">Joined</th>
                    <th className="table-header">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {data?.data.map((customer) => {
                    const status = getCustomerStatus(customer);
                    
                    return (
                      <tr key={customer.id} className="hover:bg-gray-50">
                        <td className="table-cell">
                          <div className="flex items-center">
                            <div className="h-10 w-10 flex-shrink-0">
                              <div className="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                                <span className="text-primary-600 font-medium text-sm">
                                  {customer.name.charAt(0).toUpperCase()}
                                </span>
                              </div>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">
                                {customer.name}
                              </div>
                              <div className="text-sm text-gray-500">
                                ID: {customer.id.slice(0, 8)}...
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="table-cell">
                          <div className="space-y-1">
                            <div className="flex items-center text-sm text-gray-900">
                              <PhoneIcon className="h-4 w-4 mr-2 text-gray-400" />
                              {customer.phone}
                            </div>
                            {customer.email && (
                              <div className="flex items-center text-sm text-gray-500">
                                <EnvelopeIcon className="h-4 w-4 mr-2 text-gray-400" />
                                {customer.email}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="table-cell">
                          {customer.address ? (
                            <div className="flex items-start text-sm text-gray-900">
                              <MapPinIcon className="h-4 w-4 mr-2 text-gray-400 mt-0.5" />
                              <div>
                                <div>{customer.address.city}</div>
                                {customer.address.state && (
                                  <div className="text-gray-500">{customer.address.state}</div>
                                )}
                              </div>
                            </div>
                          ) : (
                            <span className="text-sm text-gray-400">No address</span>
                          )}
                        </td>
                        <td className="table-cell">
                          <div className="flex items-center text-sm text-gray-900">
                            <ShoppingBagIcon className="h-4 w-4 mr-2 text-gray-400" />
                            {customer.total_orders}
                          </div>
                          {customer.last_order_date && (
                            <div className="text-xs text-gray-500">
                              Last: {formatDate(customer.last_order_date)}
                            </div>
                          )}
                        </td>
                        <td className="table-cell">
                          <div className="text-sm font-medium text-gray-900">
                            {formatPrice(customer.total_spent)}
                          </div>
                        </td>
                        <td className="table-cell">
                          <span className={`badge ${status.class}`}>
                            {status.label}
                          </span>
                        </td>
                        <td className="table-cell">
                          <div className="text-sm text-gray-900">
                            {formatDate(customer.created_at)}
                          </div>
                        </td>
                        <td className="table-cell">
                          <div className="flex items-center space-x-2">
                            <Link
                              to={`/customers/${customer.id}`}
                              className="text-primary-600 hover:text-primary-700"
                              title="View customer details"
                            >
                              <EyeIcon className="h-4 w-4" />
                            </Link>
                            <Link
                              to={`/customers/${customer.id}/edit`}
                              className="text-gray-600 hover:text-gray-700"
                              title="Edit customer"
                            >
                              <PencilIcon className="h-4 w-4" />
                            </Link>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>

            {/* Empty state */}
            {data?.data.length === 0 && (
              <div className="text-center py-12">
                <div className="text-gray-500 mb-4">No customers found</div>
                <p className="text-sm text-gray-400">
                  Customers will appear here when they place orders or are added manually
                </p>
              </div>
            )}

            {/* Pagination */}
            {data && data.total_pages > 1 && (
              <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                <div className="flex items-center justify-between">
                  <div className="text-sm text-gray-700">
                    Showing {((filters.page || 1) - 1) * (filters.limit || 20) + 1} to{' '}
                    {Math.min((filters.page || 1) * (filters.limit || 20), data.total)} of{' '}
                    {data.total} results
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handlePageChange((filters.page || 1) - 1)}
                      disabled={filters.page === 1}
                      className="btn-outline disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>
                    <button
                      onClick={() => handlePageChange((filters.page || 1) + 1)}
                      disabled={filters.page === data.total_pages}
                      className="btn-outline disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default CustomersPage;
