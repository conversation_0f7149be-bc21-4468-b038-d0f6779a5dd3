// Authentication types for admin portal

export interface User {
  id: string;
  email: string;
  name: string;
  picture_url?: string;
  role: 'admin' | 'super_admin';
  is_active: boolean;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface LoginResponse {
  token: string;
  expires_at: string;
  user: User;
}

export interface AuthContextType extends AuthState {
  login: (redirectTo?: string) => void;
  logout: () => void;
  checkAuth: () => Promise<void>;
  clearError: () => void;
}

export interface AuthError {
  error: string;
  message: string;
  code: number;
  details?: string;
}
