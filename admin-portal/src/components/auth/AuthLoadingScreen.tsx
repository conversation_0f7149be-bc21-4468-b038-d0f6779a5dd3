import React from 'react';

interface AuthLoadingScreenProps {
  message?: string;
}

const AuthLoadingScreen: React.FC<AuthLoadingScreenProps> = ({
  message = "Checking authentication..."
}) => {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="text-center">
        {/* Logo */}
        <img
          className="mx-auto h-16 w-auto mb-8"
          src="/logo_small.JPG"
          alt="Anand Jewels"
        />

        {/* Loading Spinner */}
        <div className="relative">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <div className="absolute inset-0 rounded-full border-2 border-gray-200"></div>
        </div>

        {/* Loading Message */}
        <p className="mt-4 text-sm text-gray-600">{message}</p>

        {/* Additional Loading Dots */}
        <div className="flex justify-center mt-2 space-x-1">
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
        </div>
      </div>
    </div>
  );
};

export default AuthLoadingScreen;
