import React from 'react';
import { ExclamationTriangleIcon, XCircleIcon } from '@heroicons/react/24/outline';

interface InventoryAlert {
  product_id: string;
  product_name: string;
  product_sku: string;
  current_stock: number;
  min_stock_level: number;
}

interface InventoryAlertsProps {
  lowStockAlerts: InventoryAlert[];
  outOfStockAlerts: InventoryAlert[];
}

const InventoryAlerts: React.FC<InventoryAlertsProps> = ({ lowStockAlerts, outOfStockAlerts }) => {
  if (lowStockAlerts.length === 0 && outOfStockAlerts.length === 0) {
    return null;
  }

  return (
    <div className="space-y-4">
      {/* Out of Stock Alerts */}
      {outOfStockAlerts.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center mb-3">
            <XCircleIcon className="h-5 w-5 text-red-600 mr-2" />
            <h3 className="text-sm font-medium text-red-800">
              Out of Stock ({outOfStockAlerts.length})
            </h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {outOfStockAlerts.slice(0, 6).map((alert) => (
              <div key={alert.product_id} className="bg-white p-3 rounded border border-red-200">
                <div className="text-sm font-medium text-gray-900 truncate">
                  {alert.product_name}
                </div>
                <div className="text-xs text-gray-500">
                  SKU: {alert.product_sku}
                </div>
                <div className="text-xs text-red-600 mt-1">
                  Stock: {alert.current_stock} / Min: {alert.min_stock_level}
                </div>
              </div>
            ))}
          </div>
          {outOfStockAlerts.length > 6 && (
            <div className="mt-3 text-sm text-red-700">
              +{outOfStockAlerts.length - 6} more items out of stock
            </div>
          )}
        </div>
      )}

      {/* Low Stock Alerts */}
      {lowStockAlerts.length > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center mb-3">
            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600 mr-2" />
            <h3 className="text-sm font-medium text-yellow-800">
              Low Stock ({lowStockAlerts.length})
            </h3>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {lowStockAlerts.slice(0, 6).map((alert) => (
              <div key={alert.product_id} className="bg-white p-3 rounded border border-yellow-200">
                <div className="text-sm font-medium text-gray-900 truncate">
                  {alert.product_name}
                </div>
                <div className="text-xs text-gray-500">
                  SKU: {alert.product_sku}
                </div>
                <div className="text-xs text-yellow-600 mt-1">
                  Stock: {alert.current_stock} / Min: {alert.min_stock_level}
                </div>
              </div>
            ))}
          </div>
          {lowStockAlerts.length > 6 && (
            <div className="mt-3 text-sm text-yellow-700">
              +{lowStockAlerts.length - 6} more items with low stock
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default InventoryAlerts;
