import React from 'react';
import type { FilterOptions } from '../../types';

interface InventoryFiltersProps {
  filters: FilterOptions;
  onFilterChange: (filters: Partial<FilterOptions>) => void;
}

const categories = [
  { value: '', label: 'All Categories' },
  { value: 'rings', label: 'Rings' },
  { value: 'necklaces', label: 'Necklaces' },
  { value: 'earrings', label: 'Earrings' },
  { value: 'bracelets', label: 'Bracelets' },
  { value: 'bangles', label: 'Bangles' },
  { value: 'anklets', label: 'Anklets' },
  { value: 'nose_pins', label: 'Nose Pins' },
  { value: 'pendants', label: 'Pendants' },
];

const stockLevels = [
  { value: '', label: 'All Stock Levels' },
  { value: 'in_stock', label: 'In Stock' },
  { value: 'low_stock', label: 'Low Stock' },
  { value: 'out_of_stock', label: 'Out of Stock' },
];

const InventoryFilters: React.FC<InventoryFiltersProps> = ({ filters, onFilterChange }) => {
  const handleClearFilters = () => {
    onFilterChange({
      category: '',
    });
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {/* Category filter */}
      <div>
        <label className="form-label">Category</label>
        <select
          className="form-input"
          value={filters.category || ''}
          onChange={(e) => onFilterChange({ category: e.target.value || undefined })}
        >
          {categories.map((category) => (
            <option key={category.value} value={category.value}>
              {category.label}
            </option>
          ))}
        </select>
      </div>

      {/* Stock level filter */}
      <div>
        <label className="form-label">Stock Level</label>
        <select
          className="form-input"
          value={filters.stock_level || ''}
          onChange={(e) => onFilterChange({ stock_level: e.target.value || undefined })}
        >
          {stockLevels.map((level) => (
            <option key={level.value} value={level.value}>
              {level.label}
            </option>
          ))}
        </select>
      </div>

      {/* Clear filters */}
      <div className="flex items-end">
        <button
          onClick={handleClearFilters}
          className="btn-outline w-full"
        >
          Clear Filters
        </button>
      </div>
    </div>
  );
};

export default InventoryFilters;
