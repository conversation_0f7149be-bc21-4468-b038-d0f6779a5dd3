import React, { useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { ChartBarIcon, PlusIcon, MinusIcon } from '@heroicons/react/24/outline';
import { useMutation } from '@tanstack/react-query';
import { updateInventory } from '../../lib/api';
import type { InventoryStatus } from '../../types';

interface InventoryAdjustModalProps {
  item: InventoryStatus;
  onClose: () => void;
  onSuccess: () => void;
}

const adjustmentTypes = [
  { value: 'add', label: 'Add Stock', description: 'Increase inventory quantity', icon: PlusIcon },
  { value: 'remove', label: 'Remove Stock', description: 'Decrease inventory quantity', icon: MinusIcon },
  { value: 'set', label: 'Set Stock', description: 'Set exact inventory quantity', icon: ChartBarIcon },
];

const adjustmentReasons = [
  'Purchase/Restock',
  'Sale/Order',
  'Damage/Loss',
  'Return/Refund',
  'Transfer',
  'Audit Adjustment',
  'Manufacturing',
  'Other',
];

const InventoryAdjustModal: React.FC<InventoryAdjustModalProps> = ({
  item,
  onClose,
  onSuccess,
}) => {
  const [adjustmentType, setAdjustmentType] = useState<'add' | 'remove' | 'set'>('add');
  const [quantity, setQuantity] = useState<number>(1);
  const [reason, setReason] = useState<string>('');
  const [customReason, setCustomReason] = useState<string>('');

  const updateMutation = useMutation({
    mutationFn: (data: { quantity: number; change_type: string; reason: string }) =>
      updateInventory(item.product_id, data),
    onSuccess: () => {
      onSuccess();
    },
    onError: (error) => {
      console.error('Failed to update inventory:', error);
      // You could add toast notification here
    },
  });

  const handleSubmit = () => {
    if (quantity <= 0) return;
    
    let finalQuantity = quantity;
    let changeType = adjustmentType;
    
    if (adjustmentType === 'set') {
      // Convert "set" to add/remove based on current stock
      if (quantity > item.current_stock) {
        finalQuantity = quantity - item.current_stock;
        changeType = 'add';
      } else if (quantity < item.current_stock) {
        finalQuantity = item.current_stock - quantity;
        changeType = 'remove';
      } else {
        // No change needed
        onClose();
        return;
      }
    }

    const finalReason = reason === 'Other' ? customReason : reason;
    
    updateMutation.mutate({
      quantity: finalQuantity,
      change_type: changeType,
      reason: finalReason,
    });
  };

  const getNewStock = () => {
    switch (adjustmentType) {
      case 'add':
        return item.current_stock + quantity;
      case 'remove':
        return Math.max(0, item.current_stock - quantity);
      case 'set':
        return quantity;
      default:
        return item.current_stock;
    }
  };

  const isValid = quantity > 0 && reason && (reason !== 'Other' || customReason.trim());

  return (
    <Transition.Root show={true} as={React.Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={React.Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={React.Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div>
                  <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                    <ChartBarIcon className="h-6 w-6 text-blue-600" aria-hidden="true" />
                  </div>
                  <div className="mt-3 text-center sm:mt-5">
                    <Dialog.Title as="h3" className="text-base font-semibold leading-6 text-gray-900">
                      Adjust Inventory
                    </Dialog.Title>
                    <div className="mt-2">
                      <div className="text-sm text-gray-500 mb-4">
                        <div className="bg-gray-50 p-3 rounded-lg text-left">
                          <div className="font-medium text-gray-900">{item.product_name}</div>
                          <div className="text-sm text-gray-600">SKU: {item.product_sku}</div>
                          <div className="text-sm text-gray-600">
                            Current Stock: <span className="font-medium">{item.current_stock}</span>
                          </div>
                          <div className="text-sm text-gray-600">
                            Min Level: <span className="font-medium">{item.min_stock_level}</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className="space-y-4 text-left">
                        {/* Adjustment Type */}
                        <div>
                          <label className="form-label">Adjustment Type</label>
                          <div className="space-y-2">
                            {adjustmentTypes.map((type) => (
                              <label
                                key={type.value}
                                className={`flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                                  adjustmentType === type.value
                                    ? 'border-primary-500 bg-primary-50'
                                    : 'border-gray-200'
                                }`}
                              >
                                <input
                                  type="radio"
                                  name="adjustmentType"
                                  value={type.value}
                                  checked={adjustmentType === type.value}
                                  onChange={(e) => setAdjustmentType(e.target.value as any)}
                                  className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                                />
                                <type.icon className="ml-3 h-5 w-5 text-gray-400" />
                                <div className="ml-3">
                                  <div className="text-sm font-medium text-gray-900">
                                    {type.label}
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    {type.description}
                                  </div>
                                </div>
                              </label>
                            ))}
                          </div>
                        </div>

                        {/* Quantity */}
                        <div>
                          <label className="form-label">
                            {adjustmentType === 'set' ? 'New Stock Level' : 'Quantity'}
                          </label>
                          <input
                            type="number"
                            min="0"
                            className="form-input"
                            value={quantity}
                            onChange={(e) => setQuantity(Math.max(0, parseInt(e.target.value) || 0))}
                          />
                          <div className="text-xs text-gray-500 mt-1">
                            New stock will be: <span className="font-medium">{getNewStock()}</span>
                          </div>
                        </div>

                        {/* Reason */}
                        <div>
                          <label className="form-label">Reason</label>
                          <select
                            className="form-input"
                            value={reason}
                            onChange={(e) => setReason(e.target.value)}
                          >
                            <option value="">Select reason</option>
                            {adjustmentReasons.map((reasonOption) => (
                              <option key={reasonOption} value={reasonOption}>
                                {reasonOption}
                              </option>
                            ))}
                          </select>
                        </div>

                        {/* Custom Reason */}
                        {reason === 'Other' && (
                          <div>
                            <label className="form-label">Custom Reason</label>
                            <input
                              type="text"
                              className="form-input"
                              placeholder="Enter custom reason"
                              value={customReason}
                              onChange={(e) => setCustomReason(e.target.value)}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
                <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                  <button
                    type="button"
                    className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={!isValid || updateMutation.isPending}
                    onClick={handleSubmit}
                  >
                    {updateMutation.isPending ? 'Updating...' : 'Update Inventory'}
                  </button>
                  <button
                    type="button"
                    className="btn-outline mt-3 sm:mt-0"
                    onClick={onClose}
                    disabled={updateMutation.isPending}
                  >
                    Cancel
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default InventoryAdjustModal;
