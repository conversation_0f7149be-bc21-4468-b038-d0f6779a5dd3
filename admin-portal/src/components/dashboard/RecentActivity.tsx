import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import {
  ShoppingBagIcon,
  CubeIcon,
  RectangleStackIcon,
  UsersIcon,
  ExclamationTriangleIcon,
  ArrowTrendingUpIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';
import { getRecentActivities } from '../../lib/api';
import type { Activity, ActivityType } from '../../types';

const RecentActivity: React.FC = () => {
  const { data: activitiesData, isLoading, error } = useQuery({
    queryKey: ['recent-activities'],
    queryFn: () => getRecentActivities({ limit: 10 }),
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  const getActivityIcon = (type: ActivityType) => {
    switch (type) {
      case 'order_created':
      case 'order_updated':
        return ShoppingBagIcon;
      case 'product_created':
      case 'product_updated':
      case 'product_deleted':
        return CubeIcon;
      case 'collection_created':
      case 'collection_updated':
        return RectangleStackIcon;
      case 'customer_created':
        return UsersIcon;
      case 'inventory_updated':
        return ArrowTrendingUpIcon;
      case 'low_stock_alert':
        return ExclamationTriangleIcon;
      default:
        return ClockIcon;
    }
  };

  const getActivityColor = (type: ActivityType) => {
    switch (type) {
      case 'order_created':
        return 'text-green-600 bg-green-100';
      case 'order_updated':
        return 'text-blue-600 bg-blue-100';
      case 'product_created':
      case 'collection_created':
      case 'customer_created':
        return 'text-purple-600 bg-purple-100';
      case 'product_updated':
      case 'collection_updated':
        return 'text-blue-600 bg-blue-100';
      case 'product_deleted':
        return 'text-red-600 bg-red-100';
      case 'inventory_updated':
        return 'text-indigo-600 bg-indigo-100';
      case 'low_stock_alert':
        return 'text-orange-600 bg-orange-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getEntityLink = (activity: Activity) => {
    switch (activity.entity_type) {
      case 'product':
        return `/products/${activity.entity_id}/edit`;
      case 'collection':
        return `/collections/${activity.entity_id}/edit`;
      case 'order':
        return `/orders/${activity.entity_id}`;
      case 'customer':
        return `/customers/${activity.entity_id}`;
      default:
        return null;
    }
  };

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
      const hours = Math.floor(diffInSeconds / 3600);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    }
  };

  const renderActivityMetadata = (activity: Activity) => {
    if (!activity.metadata) return null;

    switch (activity.type) {
      case 'order_created':
        return (
          <div className="text-xs text-gray-500 mt-1">
            ₹{activity.metadata.order_total?.toLocaleString()} • {activity.metadata.items_count} items
          </div>
        );
      case 'inventory_updated':
        return (
          <div className="text-xs text-gray-500 mt-1">
            {activity.metadata.old_quantity} → {activity.metadata.new_quantity} units
          </div>
        );
      case 'low_stock_alert':
        return (
          <div className="text-xs text-orange-600 mt-1">
            {activity.metadata.current_stock} left (threshold: {activity.metadata.threshold})
          </div>
        );
      case 'collection_created':
        return (
          <div className="text-xs text-gray-500 mt-1">
            {activity.metadata.products_count} products
          </div>
        );
      default:
        return null;
    }
  };

  if (isLoading) {
    return (
      <>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-medium text-gray-900">Recent Activity</h2>
          <Link
            to="/activities"
            className="text-sm text-primary-600 hover:text-primary-700"
          >
            View all
          </Link>
        </div>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-start space-x-3 animate-pulse">
              <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
      </>
    );
  }

  if (error) {
    return (
      <>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-medium text-gray-900">Recent Activity</h2>
          <Link
            to="/activities"
            className="text-sm text-primary-600 hover:text-primary-700"
          >
            View all
          </Link>
        </div>
        <div className="text-center py-8 text-red-500">
          <ExclamationTriangleIcon className="h-12 w-12 mx-auto mb-2" />
          <p>Failed to load recent activities</p>
        </div>
      </>
    );
  }

  return (
    <>
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-medium text-gray-900">Recent Activity</h2>
        <Link
          to="/activities"
          className="text-sm text-primary-600 hover:text-primary-700"
        >
          View all
        </Link>
      </div>

      {activitiesData?.data?.length ? (
        <div className="space-y-4">
          {activitiesData.data.map((activity) => {
            const Icon = getActivityIcon(activity.type);
            const colorClasses = getActivityColor(activity.type);
            const entityLink = getEntityLink(activity);

            return (
              <div key={activity.id} className="flex items-start space-x-3">
                <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${colorClasses}`}>
                  <Icon className="h-4 w-4" />
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <p className="text-sm font-medium text-gray-900">
                      {activity.title}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatTimeAgo(activity.created_at)}
                    </p>
                  </div>
                  <p className="text-sm text-gray-600">
                    {activity.description}
                  </p>
                  {renderActivityMetadata(activity)}
                  {entityLink && (
                    <Link
                      to={entityLink}
                      className="text-xs text-primary-600 hover:text-primary-700 mt-1 inline-block"
                    >
                      View details →
                    </Link>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="text-center py-8 text-gray-500">
          <ClockIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
          <p>No recent activity</p>
          <p className="text-sm mt-1">Activities will appear here as they happen</p>
        </div>
      )}
    </>
  );
};

export default RecentActivity;
