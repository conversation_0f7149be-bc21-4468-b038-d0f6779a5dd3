import React, { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { XMarkIcon, MagnifyingGlassIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import { getCollections, addProductToCollection } from '../../lib/api';
import type { Product, Collection, FilterOptions } from '../../types';

interface ProductCollectionModalProps {
  product: Product;
  onClose: () => void;
  onSuccess: (message: string) => void;
}

const ProductCollectionModal: React.FC<ProductCollectionModalProps> = ({
  product,
  onClose,
  onSuccess,
}) => {
  const [search, setSearch] = useState('');
  const [selectedCollection, setSelectedCollection] = useState<Collection | null>(null);
  const queryClient = useQueryClient();

  // Fetch collections with search
  const { data: collectionsData, isLoading } = useQuery({
    queryKey: ['collections', { search, limit: 20, is_active: true }],
    queryFn: () => getCollections({ 
      search, 
      limit: 20, 
      is_active: true,
      page: 1,
      // Sort by most recent first
      sort_by: 'created_at',
      sort_order: 'desc'
    } as FilterOptions),
  });

  // Add product to collection mutation
  const addToCollectionMutation = useMutation({
    mutationFn: ({ collectionId, productId }: { collectionId: string; productId: string }) =>
      addProductToCollection(collectionId, productId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['collections'] });
      onSuccess(`Product "${product.name}" added to collection "${selectedCollection?.name}" successfully!`);
      onClose();
    },
    onError: (error: any) => {
      console.error('Failed to add product to collection:', error);
      // Handle error - could show error message
    },
  });

  const handleAddToCollection = () => {
    if (selectedCollection) {
      addToCollectionMutation.mutate({
        collectionId: selectedCollection.id,
        productId: product.id,
      });
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">
            Add to Collection
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* Product info */}
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="text-sm font-medium text-gray-900">{product.name}</div>
          <div className="text-sm text-gray-500">SKU: {product.sku}</div>
        </div>

        {/* Search */}
        <div className="mb-4">
          <div className="relative">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search collections..."
              className="form-input pl-10"
              value={search}
              onChange={(e) => setSearch(e.target.value)}
            />
          </div>
        </div>

        {/* Collections list */}
        <div className="mb-6">
          <div className="max-h-64 overflow-y-auto border border-gray-200 rounded-lg">
            {isLoading ? (
              <div className="p-4 text-center text-gray-500">Loading collections...</div>
            ) : collectionsData?.data?.length ? (
              <div className="divide-y divide-gray-200">
                {collectionsData.data.map((collection) => (
                  <div
                    key={collection.id}
                    className={`p-3 cursor-pointer hover:bg-gray-50 ${
                      selectedCollection?.id === collection.id ? 'bg-primary-50 border-primary-200' : ''
                    }`}
                    onClick={() => setSelectedCollection(collection)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900">
                          {collection.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {collection.product_count || 0} products
                        </div>
                      </div>
                      {selectedCollection?.id === collection.id && (
                        <CheckCircleIcon className="h-5 w-5 text-primary-600" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="p-4 text-center text-gray-500">
                {search ? 'No collections found matching your search.' : 'No collections available.'}
              </div>
            )}
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="btn-outline"
          >
            Cancel
          </button>
          <button
            onClick={handleAddToCollection}
            disabled={!selectedCollection || addToCollectionMutation.isPending}
            className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {addToCollectionMutation.isPending ? 'Adding...' : 'Add to Collection'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ProductCollectionModal;
