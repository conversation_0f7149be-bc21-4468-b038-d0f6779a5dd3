import React from 'react';
import type { FilterOptions } from '../../types';

interface ProductFiltersProps {
  filters: FilterOptions;
  onFilterChange: (filters: Partial<FilterOptions>) => void;
}

const categories = [
  { value: '', label: 'All Categories' },
  { value: 'rings', label: 'Rings' },
  { value: 'necklaces', label: 'Necklaces' },
  { value: 'earrings', label: 'Earrings' },
  { value: 'bracelets', label: 'Bracelets' },
  { value: 'bangles', label: 'Bangles' },
  { value: 'anklets', label: 'Anklets' },
  { value: 'nose_pins', label: 'Nose Pins' },
  { value: 'pendants', label: 'Pendants' },
];

const ProductFilters: React.FC<ProductFiltersProps> = ({ filters, onFilterChange }) => {
  const handleClearFilters = () => {
    onFilterChange({
      category: '',
      is_featured: undefined,
      is_active: undefined,
    });
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      {/* Category filter */}
      <div>
        <label className="form-label">Category</label>
        <select
          className="form-input"
          value={filters.category || ''}
          onChange={(e) => onFilterChange({ category: e.target.value || undefined })}
        >
          {categories.map((category) => (
            <option key={category.value} value={category.value}>
              {category.label}
            </option>
          ))}
        </select>
      </div>

      {/* Featured filter */}
      <div>
        <label className="form-label">Featured</label>
        <select
          className="form-input"
          value={filters.is_featured === undefined ? '' : filters.is_featured.toString()}
          onChange={(e) => {
            const value = e.target.value;
            onFilterChange({
              is_featured: value === '' ? undefined : value === 'true'
            });
          }}
        >
          <option value="">All Products</option>
          <option value="true">Featured Only</option>
          <option value="false">Not Featured</option>
        </select>
      </div>

      {/* Status filter */}
      <div>
        <label className="form-label">Status</label>
        <select
          className="form-input"
          value={filters.is_active === undefined ? '' : filters.is_active.toString()}
          onChange={(e) => {
            const value = e.target.value;
            onFilterChange({
              is_active: value === '' ? undefined : value === 'true'
            });
          }}
        >
          <option value="">All Status</option>
          <option value="true">Active</option>
          <option value="false">Inactive</option>
        </select>
      </div>

      {/* Clear filters */}
      <div className="flex items-end">
        <button
          onClick={handleClearFilters}
          className="btn-outline w-full"
        >
          Clear Filters
        </button>
      </div>
    </div>
  );
};

export default ProductFilters;
