import React from 'react';
import type { FilterOptions } from '../../types';

interface OrderFiltersProps {
  filters: FilterOptions;
  onFilterChange: (filters: Partial<FilterOptions>) => void;
}

const orderStatuses = [
  { value: '', label: 'All Status' },
  { value: 'pending', label: 'Pending' },
  { value: 'confirmed', label: 'Confirmed' },
  { value: 'processing', label: 'Processing' },
  { value: 'shipped', label: 'Shipped' },
  { value: 'delivered', label: 'Delivered' },
  { value: 'cancelled', label: 'Cancelled' },
];

const OrderFilters: React.FC<OrderFiltersProps> = ({ filters, onFilterChange }) => {
  const handleClearFilters = () => {
    onFilterChange({
      status: '',
      date_from: '',
      date_to: '',
    });
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      {/* Status filter */}
      <div>
        <label className="form-label">Status</label>
        <select
          className="form-input"
          value={filters.status || ''}
          onChange={(e) => onFilterChange({ status: e.target.value || undefined })}
        >
          {orderStatuses.map((status) => (
            <option key={status.value} value={status.value}>
              {status.label}
            </option>
          ))}
        </select>
      </div>

      {/* Date from filter */}
      <div>
        <label className="form-label">From Date</label>
        <input
          type="date"
          className="form-input"
          value={filters.date_from || ''}
          onChange={(e) => onFilterChange({ date_from: e.target.value || undefined })}
        />
      </div>

      {/* Date to filter */}
      <div>
        <label className="form-label">To Date</label>
        <input
          type="date"
          className="form-input"
          value={filters.date_to || ''}
          onChange={(e) => onFilterChange({ date_to: e.target.value || undefined })}
        />
      </div>

      {/* Clear filters */}
      <div className="flex items-end">
        <button
          onClick={handleClearFilters}
          className="btn-outline w-full"
        >
          Clear Filters
        </button>
      </div>
    </div>
  );
};

export default OrderFilters;
