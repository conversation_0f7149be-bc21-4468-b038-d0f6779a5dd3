import React, { useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { CheckCircleIcon } from '@heroicons/react/24/outline';
import { useMutation } from '@tanstack/react-query';
import { updateOrderStatus } from '../../lib/api';
import type { Order } from '../../types';

interface OrderStatusModalProps {
  order: Order;
  onClose: () => void;
  onSuccess: () => void;
}

const statusOptions = [
  { value: 'pending', label: 'Pending', description: 'Order received, awaiting confirmation' },
  { value: 'confirmed', label: 'Confirmed', description: 'Order confirmed by admin' },
  { value: 'processing', label: 'Processing', description: 'Order is being prepared' },
  { value: 'shipped', label: 'Shipped', description: 'Order has been shipped' },
  { value: 'delivered', label: 'Delivered', description: 'Order delivered to customer' },
  { value: 'cancelled', label: 'Cancelled', description: 'Order has been cancelled' },
];

const OrderStatusModal: React.FC<OrderStatusModalProps> = ({
  order,
  onClose,
  onSuccess,
}) => {
  const [selectedStatus, setSelectedStatus] = useState(order.status);

  const updateMutation = useMutation({
    mutationFn: (status: string) => updateOrderStatus(order.id, status),
    onSuccess: () => {
      onSuccess();
    },
    onError: (error) => {
      console.error('Failed to update order status:', error);
      // You could add toast notification here
    },
  });

  const handleUpdate = () => {
    if (selectedStatus !== order.status) {
      updateMutation.mutate(selectedStatus);
    } else {
      onClose();
    }
  };

  const getStatusColor = (status: string) => {
    const colors: Record<string, string> = {
      pending: 'text-yellow-600',
      confirmed: 'text-blue-600',
      processing: 'text-blue-600',
      shipped: 'text-green-600',
      delivered: 'text-green-600',
      cancelled: 'text-red-600',
    };
    return colors[status] || 'text-gray-600';
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
    }).format(price);
  };

  return (
    <Transition.Root show={true} as={React.Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={React.Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={React.Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div>
                  <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                    <CheckCircleIcon className="h-6 w-6 text-blue-600" aria-hidden="true" />
                  </div>
                  <div className="mt-3 text-center sm:mt-5">
                    <Dialog.Title as="h3" className="text-base font-semibold leading-6 text-gray-900">
                      Update Order Status
                    </Dialog.Title>
                    <div className="mt-2">
                      <div className="text-sm text-gray-500 mb-4">
                        <div className="bg-gray-50 p-3 rounded-lg text-left">
                          <div className="font-medium text-gray-900">Order #{order.order_number}</div>
                          <div className="text-sm text-gray-600">
                            Customer: {order.customer?.name || 'Unknown'}
                          </div>
                          <div className="text-sm text-gray-600">
                            Amount: {formatPrice(order.final_amount)}
                          </div>
                          <div className="text-sm text-gray-600">
                            Items: {order.items?.length || 0}
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-left">
                        <label className="form-label">Select New Status</label>
                        <div className="space-y-2">
                          {statusOptions.map((option) => (
                            <label
                              key={option.value}
                              className={`flex items-start p-3 border rounded-lg cursor-pointer hover:bg-gray-50 ${
                                selectedStatus === option.value
                                  ? 'border-primary-500 bg-primary-50'
                                  : 'border-gray-200'
                              }`}
                            >
                              <input
                                type="radio"
                                name="status"
                                value={option.value}
                                checked={selectedStatus === option.value}
                                onChange={(e) => setSelectedStatus(e.target.value as any)}
                                className="mt-1 h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                              />
                              <div className="ml-3">
                                <div className={`text-sm font-medium ${getStatusColor(option.value)}`}>
                                  {option.label}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {option.description}
                                </div>
                              </div>
                            </label>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="mt-5 sm:mt-6 sm:grid sm:grid-flow-row-dense sm:grid-cols-2 sm:gap-3">
                  <button
                    type="button"
                    className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={updateMutation.isPending}
                    onClick={handleUpdate}
                  >
                    {updateMutation.isPending ? 'Updating...' : 'Update Status'}
                  </button>
                  <button
                    type="button"
                    className="btn-outline mt-3 sm:mt-0"
                    onClick={onClose}
                    disabled={updateMutation.isPending}
                  >
                    Cancel
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default OrderStatusModal;
