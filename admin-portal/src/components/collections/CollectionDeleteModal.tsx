import React, { useState } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { useMutation } from '@tanstack/react-query';
import { deleteCollection } from '../../lib/api';
import type { Collection } from '../../types';

interface CollectionDeleteModalProps {
  collection: Collection;
  onClose: () => void;
  onSuccess: () => void;
}

const CollectionDeleteModal: React.FC<CollectionDeleteModalProps> = ({
  collection,
  onClose,
  onSuccess,
}) => {
  const [confirmText, setConfirmText] = useState('');

  const deleteMutation = useMutation({
    mutationFn: () => deleteCollection(collection.id),
    onSuccess: () => {
      onSuccess();
    },
    onError: (error) => {
      console.error('Failed to delete collection:', error);
      // You could add toast notification here
    },
  });

  const handleDelete = () => {
    if (confirmText === collection.name) {
      deleteMutation.mutate();
    }
  };

  const isConfirmValid = confirmText === collection.name;

  return (
    <Transition.Root show={true} as={React.Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={React.Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <Transition.Child
              as={React.Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
              enterTo="opacity-100 translate-y-0 sm:scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 translate-y-0 sm:scale-100"
              leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            >
              <Dialog.Panel className="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                <div className="sm:flex sm:items-start">
                  <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                    <ExclamationTriangleIcon
                      className="h-6 w-6 text-red-600"
                      aria-hidden="true"
                    />
                  </div>
                  <div className="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                    <Dialog.Title
                      as="h3"
                      className="text-base font-semibold leading-6 text-gray-900"
                    >
                      Delete Collection
                    </Dialog.Title>
                    <div className="mt-2">
                      <p className="text-sm text-gray-500">
                        Are you sure you want to delete "{collection.name}"? This action cannot be undone.
                        All associated data including product associations and sharing links will be removed.
                      </p>
                      <div className="mt-4">
                        <label className="form-label">
                          Type the collection name to confirm deletion:
                        </label>
                        <input
                          type="text"
                          className="form-input mt-1"
                          placeholder={collection.name}
                          value={confirmText}
                          onChange={(e) => setConfirmText(e.target.value)}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                  <button
                    type="button"
                    className="btn-danger disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={!isConfirmValid || deleteMutation.isPending}
                    onClick={handleDelete}
                  >
                    {deleteMutation.isPending ? 'Deleting...' : 'Delete Collection'}
                  </button>
                  <button
                    type="button"
                    className="btn-outline mt-3 sm:mt-0 sm:mr-3"
                    onClick={onClose}
                    disabled={deleteMutation.isPending}
                  >
                    Cancel
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default CollectionDeleteModal;
