import React from 'react';
import type { FilterOptions } from '../../types';

interface CollectionFiltersProps {
  filters: FilterOptions;
  onFilterChange: (filters: Partial<FilterOptions>) => void;
}

const CollectionFilters: React.FC<CollectionFiltersProps> = ({ filters, onFilterChange }) => {
  const handleClearFilters = () => {
    onFilterChange({
      is_active: undefined,
      is_public: undefined,
    });
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {/* Status filter */}
      <div>
        <label className="form-label">Status</label>
        <select
          className="form-input"
          value={filters.is_active === undefined ? '' : filters.is_active.toString()}
          onChange={(e) => {
            const value = e.target.value;
            onFilterChange({
              is_active: value === '' ? undefined : value === 'true'
            });
          }}
        >
          <option value="">All Status</option>
          <option value="true">Active</option>
          <option value="false">Inactive</option>
        </select>
      </div>

      {/* Visibility filter */}
      <div>
        <label className="form-label">Visibility</label>
        <select
          className="form-input"
          value={filters.is_public === undefined ? '' : filters.is_public.toString()}
          onChange={(e) => {
            const value = e.target.value;
            onFilterChange({
              is_public: value === '' ? undefined : value === 'true'
            });
          }}
        >
          <option value="">All Collections</option>
          <option value="true">Public</option>
          <option value="false">Private</option>
        </select>
      </div>

      {/* Clear filters */}
      <div className="flex items-end">
        <button
          onClick={handleClearFilters}
          className="btn-outline w-full"
        >
          Clear Filters
        </button>
      </div>
    </div>
  );
};

export default CollectionFilters;
