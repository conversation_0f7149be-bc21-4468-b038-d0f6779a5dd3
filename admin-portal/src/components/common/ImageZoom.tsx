import React, { useState, useCallback, useRef } from 'react';

interface ImageZoomProps {
  src: string;
  alt?: string;
  className?: string;
}

const ImageZoom: React.FC<ImageZoomProps> = ({ src, alt = '', className = '' }) => {
  const [isZoomed, setIsZoomed] = useState(false);
  const [zoomPosition, setZoomPosition] = useState({ x: 0, y: 0 });
  const imageRef = useRef<HTMLImageElement>(null);

  const handleImageClick = useCallback(() => {
    if (!isZoomed) {
      // Zoom in
      setIsZoomed(true);
    } else {
      // Reset zoom
      setIsZoomed(false);
      setZoomPosition({ x: 0, y: 0 });
    }
  }, [isZoomed]);

  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLImageElement>) => {
    if (!isZoomed) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width - 0.5) * 100;
    const y = ((e.clientY - rect.top) / rect.height - 0.5) * 100;
    setZoomPosition({ x: -x, y: -y });
  }, [isZoomed]);

  return (
    <div className={`relative overflow-hidden ${className}`}>
      <img
        ref={imageRef}
        src={src}
        alt={alt}
        className="w-full h-full object-contain cursor-pointer transition-transform duration-200"
        style={{
          transform: isZoomed ? `scale(2) translate(${zoomPosition.x}%, ${zoomPosition.y}%)` : 'scale(1)',
          transformOrigin: 'center center'
        }}
        onClick={handleImageClick}
        onMouseMove={handleMouseMove}
        loading="lazy"
      />
      
      {/* Zoom indicator */}
      {isZoomed && (
        <div className="absolute top-2 right-2 bg-black bg-opacity-50 rounded px-2 py-1 text-white text-xs">
          Zoomed 2x
        </div>
      )}
      
      {/* Instructions */}
      {isZoomed && (
        <div className="absolute bottom-2 left-2 right-2 text-center">
          <div className="bg-black bg-opacity-50 rounded px-2 py-1 text-white text-xs">
            Move mouse to pan • Click to reset zoom
          </div>
        </div>
      )}
    </div>
  );
};

export default ImageZoom;
