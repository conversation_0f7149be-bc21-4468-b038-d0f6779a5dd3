import React, { useState, useRef, useCallback } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { XMarkIcon, ChevronLeftIcon, ChevronRightIcon, MagnifyingGlassPlusIcon, MagnifyingGlassMinusIcon } from '@heroicons/react/24/outline';
import { getOptimizedProductImageUrl, getBestImageSize } from '../../lib/imageUtils';
import type { ProductImage } from '../../types';

interface ImageGalleryProps {
  images: ProductImage[];
  isOpen: boolean;
  onClose: () => void;
  initialIndex?: number;
}

const ImageGallery: React.FC<ImageGalleryProps> = ({
  images,
  isOpen,
  onClose,
  initialIndex = 0,
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const [isZoomed, setIsZoomed] = useState(false);
  const [zoomLevel, setZoomLevel] = useState(1);
  const [zoomPosition, setZoomPosition] = useState({ x: 0, y: 0 });
  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const goToPrevious = () => {
    setCurrentIndex((prev) => (prev === 0 ? images.length - 1 : prev - 1));
    resetZoom();
  };

  const goToNext = () => {
    setCurrentIndex((prev) => (prev === images.length - 1 ? 0 : prev + 1));
    resetZoom();
  };

  const resetZoom = () => {
    setIsZoomed(false);
    setZoomLevel(1);
    setZoomPosition({ x: 0, y: 0 });
  };

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 0.5, 3));
    setIsZoomed(true);
  };

  const handleZoomOut = () => {
    const newZoomLevel = Math.max(zoomLevel - 0.5, 1);
    setZoomLevel(newZoomLevel);
    if (newZoomLevel === 1) {
      setIsZoomed(false);
      setZoomPosition({ x: 0, y: 0 });
    }
  };

  const handleImageClick = useCallback((e: React.MouseEvent<HTMLImageElement>) => {
    if (!isZoomed) {
      handleZoomIn();
      return;
    }

    // Calculate zoom position based on click
    const rect = e.currentTarget.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width - 0.5) * 100;
    const y = ((e.clientY - rect.top) / rect.height - 0.5) * 100;
    setZoomPosition({ x: -x, y: -y });
  }, [isZoomed]);

  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLImageElement>) => {
    if (!isZoomed || zoomLevel <= 1) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const x = ((e.clientX - rect.left) / rect.width - 0.5) * 100;
    const y = ((e.clientY - rect.top) / rect.height - 0.5) * 100;
    setZoomPosition({ x: -x, y: -y });
  }, [isZoomed, zoomLevel]);

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowLeft') {
      goToPrevious();
    } else if (e.key === 'ArrowRight') {
      goToNext();
    } else if (e.key === 'Escape') {
      if (isZoomed) {
        resetZoom();
      } else {
        onClose();
      }
    } else if (e.key === '+' || e.key === '=') {
      handleZoomIn();
    } else if (e.key === '-') {
      handleZoomOut();
    } else if (e.key === '0') {
      resetZoom();
    }
  };

  if (!images || images.length === 0) {
    return null;
  }

  const currentImage = images[currentIndex];

  return (
    <Transition.Root show={isOpen} as={React.Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={React.Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-90 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 z-10 overflow-y-auto">
          <div 
            className="flex min-h-full items-center justify-center p-4"
            onKeyDown={handleKeyDown}
            tabIndex={0}
          >
            <Transition.Child
              as={React.Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="relative w-full max-w-4xl">
                {/* Close button */}
                <button
                  onClick={onClose}
                  className="absolute top-4 right-4 z-10 p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-colors"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>

                {/* Navigation buttons */}
                {images.length > 1 && (
                  <>
                    <button
                      onClick={goToPrevious}
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 z-10 p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-colors"
                    >
                      <ChevronLeftIcon className="h-6 w-6" />
                    </button>
                    <button
                      onClick={goToNext}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 z-10 p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-colors"
                    >
                      <ChevronRightIcon className="h-6 w-6" />
                    </button>
                  </>
                )}

                {/* Zoom controls */}
                <div className="absolute top-4 right-4 z-10 flex space-x-2">
                  <button
                    onClick={handleZoomIn}
                    disabled={zoomLevel >= 3}
                    className="p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Zoom in"
                  >
                    <MagnifyingGlassPlusIcon className="h-5 w-5" />
                  </button>
                  <button
                    onClick={handleZoomOut}
                    disabled={zoomLevel <= 1}
                    className="p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    title="Zoom out"
                  >
                    <MagnifyingGlassMinusIcon className="h-5 w-5" />
                  </button>
                  {isZoomed && (
                    <button
                      onClick={resetZoom}
                      className="p-2 bg-black bg-opacity-50 rounded-full text-white hover:bg-opacity-70 transition-colors"
                      title="Reset zoom"
                    >
                      <span className="text-xs font-medium">1:1</span>
                    </button>
                  )}
                </div>

                {/* Main image */}
                <div
                  ref={containerRef}
                  className="flex items-center justify-center overflow-hidden"
                  style={{ cursor: isZoomed ? 'move' : 'zoom-in' }}
                >
                  <img
                    ref={imageRef}
                    src={getOptimizedProductImageUrl(currentImage, isZoomed ? getBestImageSize('ZOOM_VIEW') : getBestImageSize('GALLERY_MAIN'))}
                    alt={currentImage.alt_text}
                    className="max-w-full max-h-[80vh] object-contain transition-transform duration-200"
                    style={{
                      transform: `scale(${zoomLevel}) translate(${zoomPosition.x}%, ${zoomPosition.y}%)`,
                      transformOrigin: 'center center'
                    }}
                    onClick={handleImageClick}
                    onMouseMove={handleMouseMove}
                    loading="eager"
                  />
                </div>

                {/* Image info */}
                <div className="absolute bottom-4 left-4 right-4 text-center">
                  <div className="bg-black bg-opacity-50 rounded-lg p-3 text-white">
                    <p className="text-sm font-medium">{currentImage.alt_text || 'Product Image'}</p>
                    <div className="flex items-center justify-center space-x-4 mt-1">
                      {images.length > 1 && (
                        <p className="text-xs text-gray-300">
                          {currentIndex + 1} of {images.length}
                        </p>
                      )}
                      {isZoomed && (
                        <p className="text-xs text-gray-300">
                          Zoom: {Math.round(zoomLevel * 100)}%
                        </p>
                      )}
                    </div>
                    {isZoomed && (
                      <p className="text-xs text-gray-400 mt-1">
                        Click to pan • Scroll or +/- to zoom • ESC to reset
                      </p>
                    )}
                  </div>
                </div>

                {/* Thumbnail strip */}
                {images.length > 1 && (
                  <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2">
                    <div className="flex space-x-2 bg-black bg-opacity-50 rounded-lg p-2">
                      {images.map((image, index) => (
                        <button
                          key={image.id || index}
                          onClick={() => setCurrentIndex(index)}
                          className={`w-12 h-12 rounded overflow-hidden border-2 transition-colors ${
                            index === currentIndex
                              ? 'border-white'
                              : 'border-transparent hover:border-gray-300'
                          }`}
                        >
                          <img
                            src={getOptimizedProductImageUrl(image, getBestImageSize('LIST_THUMBNAIL'))}
                            alt={image.alt_text}
                            className="w-full h-full object-cover"
                            loading="lazy"
                          />
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  );
};

export default ImageGallery;
