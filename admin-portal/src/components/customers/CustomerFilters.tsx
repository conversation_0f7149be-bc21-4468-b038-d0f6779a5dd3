import React from 'react';
import type { FilterOptions } from '../../types';

interface CustomerFiltersProps {
  filters: FilterOptions;
  onFilterChange: (filters: Partial<FilterOptions>) => void;
}

const CustomerFilters: React.FC<CustomerFiltersProps> = ({ filters, onFilterChange }) => {
  const handleClearFilters = () => {
    onFilterChange({
      is_active: undefined,
      date_from: '',
      date_to: '',
    });
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
      {/* Status filter */}
      <div>
        <label className="form-label">Status</label>
        <select
          className="form-input"
          value={filters.is_active === undefined ? '' : filters.is_active.toString()}
          onChange={(e) => {
            const value = e.target.value;
            onFilterChange({
              is_active: value === '' ? undefined : value === 'true'
            });
          }}
        >
          <option value="">All Customers</option>
          <option value="true">Active</option>
          <option value="false">Inactive</option>
        </select>
      </div>

      {/* Date from filter */}
      <div>
        <label className="form-label">Joined From</label>
        <input
          type="date"
          className="form-input"
          value={filters.date_from || ''}
          onChange={(e) => onFilterChange({ date_from: e.target.value || undefined })}
        />
      </div>

      {/* Date to filter */}
      <div>
        <label className="form-label">Joined To</label>
        <input
          type="date"
          className="form-input"
          value={filters.date_to || ''}
          onChange={(e) => onFilterChange({ date_to: e.target.value || undefined })}
        />
      </div>

      {/* Clear filters */}
      <div className="flex items-end">
        <button
          onClick={handleClearFilters}
          className="btn-outline w-full"
        >
          Clear Filters
        </button>
      </div>
    </div>
  );
};

export default CustomerFilters;
