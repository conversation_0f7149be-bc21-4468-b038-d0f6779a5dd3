import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import type { Customer, CreateCustomerRequest } from '../../types';

const customerSchema = z.object({
  name: z.string().min(1, 'Customer name is required'),
  email: z.string().email('Invalid email address').optional().or(z.literal('')),
  phone: z.string().min(1, 'Phone number is required'),
  address: z.object({
    street: z.string().optional(),
    city: z.string().optional(),
    state: z.string().optional(),
    country: z.string().optional(),
    pincode: z.string().optional(),
  }).optional(),
});

interface CustomerFormProps {
  initialData?: Customer;
  onSubmit: (data: CreateCustomerRequest) => void;
  onCancel: () => void;
  isLoading?: boolean;
  error?: Error | null;
}

const CustomerForm: React.FC<CustomerFormProps> = ({
  initialData,
  onSubmit,
  onCancel,
  isLoading = false,
  error,
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<CreateCustomerRequest>({
    resolver: zodResolver(customerSchema),
    defaultValues: initialData ? {
      name: initialData.name,
      email: initialData.email || '',
      phone: initialData.phone,
      address: {
        street: initialData.address?.street || '',
        city: initialData.address?.city || '',
        state: initialData.address?.state || '',
        country: initialData.address?.country || '',
        pincode: initialData.address?.pincode || '',
      },
    } : {
      name: '',
      email: '',
      phone: '',
      address: {
        street: '',
        city: '',
        state: '',
        country: '',
        pincode: '',
      },
    },
  });

  const handleFormSubmit = (data: CreateCustomerRequest) => {
    // Clean up empty address fields
    const cleanedData = {
      ...data,
      email: data.email || undefined,
      address: data.address && Object.values(data.address).some(value => value?.trim()) 
        ? {
            street: data.address.street?.trim() || undefined,
            city: data.address.city?.trim() || undefined,
            state: data.address.state?.trim() || undefined,
            country: data.address.country?.trim() || undefined,
            pincode: data.address.pincode?.trim() || undefined,
          }
        : undefined,
    };
    
    onSubmit(cleanedData);
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-800">
            Failed to save customer. Please try again.
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
          
          <div>
            <label className="form-label">Full Name *</label>
            <input
              type="text"
              className="form-input"
              {...register('name')}
            />
            {errors.name && <p className="form-error">{errors.name.message}</p>}
          </div>

          <div>
            <label className="form-label">Phone Number *</label>
            <input
              type="tel"
              className="form-input"
              placeholder="+91 98765 43210"
              {...register('phone')}
            />
            {errors.phone && <p className="form-error">{errors.phone.message}</p>}
          </div>

          <div>
            <label className="form-label">Email Address</label>
            <input
              type="email"
              className="form-input"
              placeholder="<EMAIL>"
              {...register('email')}
            />
            {errors.email && <p className="form-error">{errors.email.message}</p>}
            <p className="text-xs text-gray-500 mt-1">Optional - for order confirmations</p>
          </div>
        </div>

        {/* Address Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900">Address Information</h3>
          
          <div>
            <label className="form-label">Street Address</label>
            <input
              type="text"
              className="form-input"
              placeholder="123 Main Street, Apartment 4B"
              {...register('address.street')}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="form-label">City</label>
              <input
                type="text"
                className="form-input"
                placeholder="Mumbai"
                {...register('address.city')}
              />
            </div>

            <div>
              <label className="form-label">State</label>
              <input
                type="text"
                className="form-input"
                placeholder="Maharashtra"
                {...register('address.state')}
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="form-label">Country</label>
              <input
                type="text"
                className="form-input"
                placeholder="India"
                {...register('address.country')}
              />
            </div>

            <div>
              <label className="form-label">PIN Code</label>
              <input
                type="text"
                className="form-input"
                placeholder="400001"
                {...register('address.pincode')}
              />
            </div>
          </div>

          <p className="text-xs text-gray-500">
            Address information is optional but helps with order delivery
          </p>
        </div>
      </div>

      {/* Customer Stats (for edit mode) */}
      {initialData && (
        <div className="border-t pt-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Customer Statistics</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="text-sm text-gray-600">Total Orders</div>
              <div className="text-2xl font-bold text-gray-900">{initialData.total_orders}</div>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="text-sm text-gray-600">Total Spent</div>
              <div className="text-2xl font-bold text-gray-900">
                {new Intl.NumberFormat('en-IN', {
                  style: 'currency',
                  currency: 'INR',
                }).format(initialData.total_spent)}
              </div>
            </div>
            <div className="bg-gray-50 p-4 rounded-lg">
              <div className="text-sm text-gray-600">Member Since</div>
              <div className="text-lg font-medium text-gray-900">
                {new Date(initialData.created_at).toLocaleDateString('en-IN', {
                  year: 'numeric',
                  month: 'short',
                  day: 'numeric',
                })}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Form Actions */}
      <div className="flex justify-end space-x-4 pt-6 border-t">
        <button
          type="button"
          onClick={onCancel}
          className="btn-outline"
          disabled={isLoading}
        >
          Cancel
        </button>
        <button
          type="submit"
          className="btn-primary"
          disabled={isLoading}
        >
          {isLoading ? 'Saving...' : initialData ? 'Update Customer' : 'Create Customer'}
        </button>
      </div>
    </form>
  );
};

export default CustomerForm;
