// Admin Portal App
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/auth/ProtectedRoute';
import AdminLayout from './components/layout/AdminLayout';
import LoginPage from './pages/LoginPage';
import UnauthorizedPage from './pages/UnauthorizedPage';
import Dashboard from './pages/Dashboard';
import ProductsPage from './pages/products/ProductsPage';
import ProductCreatePage from './pages/products/ProductCreatePage';
import ProductEditPage from './pages/products/ProductEditPage';
import CollectionsPage from './pages/collections/CollectionsPage';
import CollectionCreatePage from './pages/collections/CollectionCreatePage';
import CollectionEditPage from './pages/collections/CollectionEditPage';
import OrdersPage from './pages/orders/OrdersPage';
import OrderDetailPage from './pages/orders/OrderDetailPage';
import InventoryPage from './pages/inventory/InventoryPage';
import CustomersPage from './pages/customers/CustomersPage';
import CustomerCreatePage from './pages/customers/CustomerCreatePage';
import CustomerEditPage from './pages/customers/CustomerEditPage';
import ActivitiesPage from './pages/ActivitiesPage';
import SearchPage from './pages/SearchPage';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: 1,
    },
  },
});

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <Router>
          <div className="min-h-screen bg-gray-50">
            <Routes>
              {/* Public Routes */}
              <Route path="/login" element={<LoginPage />} />
              <Route path="/unauthorized" element={<UnauthorizedPage />} />

              {/* Protected Admin Routes */}
              <Route path="/" element={
                <ProtectedRoute>
                  <AdminLayout />
                </ProtectedRoute>
              }>
                <Route index element={<Dashboard />} />

                {/* Products Routes */}
                <Route path="products" element={<ProductsPage />} />
                <Route path="products/create" element={<ProductCreatePage />} />
                <Route path="products/:id/edit" element={<ProductEditPage />} />

                {/* Collections Routes */}
                <Route path="collections" element={<CollectionsPage />} />
                <Route path="collections/create" element={<CollectionCreatePage />} />
                <Route path="collections/:id/edit" element={<CollectionEditPage />} />

                {/* Orders Routes */}
                <Route path="orders" element={<OrdersPage />} />
                <Route path="orders/:id" element={<OrderDetailPage />} />

                {/* Customers Routes */}
                <Route path="customers" element={<CustomersPage />} />
                <Route path="customers/create" element={<CustomerCreatePage />} />
                <Route path="customers/:id/edit" element={<CustomerEditPage />} />

                {/* Inventory Routes */}
                <Route path="inventory" element={<InventoryPage />} />

                {/* Activities Routes */}
                <Route path="activities" element={<ActivitiesPage />} />

                {/* Search Routes */}
                <Route path="search" element={<SearchPage />} />
              </Route>
            </Routes>
          </div>
        </Router>
        <ReactQueryDevtools initialIsOpen={false} />
      </AuthProvider>
    </QueryClientProvider>
  );
}

export default App;
