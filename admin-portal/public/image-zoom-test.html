<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Zoom Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .image-container {
            position: relative;
            width: 100%;
            height: 500px;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            cursor: pointer;
            background: #f9f9f9;
        }
        
        .zoom-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
            transition: transform 0.2s ease;
            transform-origin: center center;
        }
        
        .zoom-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            display: none;
        }
        
        .zoom-instructions {
            position: absolute;
            bottom: 10px;
            left: 10px;
            right: 10px;
            text-align: center;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px;
            border-radius: 4px;
            font-size: 12px;
            display: none;
        }
        
        .zoomed .zoom-indicator,
        .zoomed .zoom-instructions {
            display: block;
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .description {
            text-align: center;
            color: #666;
            margin-bottom: 20px;
        }
        
        .url-input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .load-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-bottom: 20px;
        }
        
        .load-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Image Zoom Test</h1>
        <p class="description">
            Test the zoom functionality with any image URL. Click once to zoom in (2x), click again to reset.
        </p>
        
        <input 
            type="text" 
            class="url-input" 
            id="imageUrl" 
            placeholder="Enter image URL (e.g., http://localhost:8080/api/v1/proxy/images/product-id/image-id?size=original)"
            value="http://localhost:8080/api/v1/proxy/images/de23baf6-b0b2-404a-8d4a-62f6a45bd155/d6a6d050-3a9e-4f02-83f2-dc55d26c2460?size=original"
        >
        <button class="load-button" onclick="loadImage()">Load Image</button>
        
        <div class="image-container" id="imageContainer">
            <img 
                class="zoom-image" 
                id="zoomImage" 
                src="http://localhost:8080/api/v1/proxy/images/de23baf6-b0b2-404a-8d4a-62f6a45bd155/d6a6d050-3a9e-4f02-83f2-dc55d26c2460?size=original"
                alt="Test Image"
            >
            <div class="zoom-indicator">Zoomed 2x</div>
            <div class="zoom-instructions">Move mouse to pan • Click to reset zoom</div>
        </div>
    </div>

    <script>
        let isZoomed = false;
        let zoomPosition = { x: 0, y: 0 };
        
        const imageContainer = document.getElementById('imageContainer');
        const zoomImage = document.getElementById('zoomImage');
        
        function loadImage() {
            const url = document.getElementById('imageUrl').value;
            if (url) {
                zoomImage.src = url;
                resetZoom();
            }
        }
        
        function resetZoom() {
            isZoomed = false;
            zoomPosition = { x: 0, y: 0 };
            updateImageTransform();
            imageContainer.classList.remove('zoomed');
        }
        
        function updateImageTransform() {
            if (isZoomed) {
                zoomImage.style.transform = `scale(2) translate(${zoomPosition.x}%, ${zoomPosition.y}%)`;
            } else {
                zoomImage.style.transform = 'scale(1)';
            }
        }
        
        zoomImage.addEventListener('click', function(e) {
            if (!isZoomed) {
                // Zoom in
                isZoomed = true;
                imageContainer.classList.add('zoomed');
            } else {
                // Reset zoom
                resetZoom();
            }
            updateImageTransform();
        });
        
        zoomImage.addEventListener('mousemove', function(e) {
            if (!isZoomed) return;
            
            const rect = e.currentTarget.getBoundingClientRect();
            const x = ((e.clientX - rect.left) / rect.width - 0.5) * 100;
            const y = ((e.clientY - rect.top) / rect.height - 0.5) * 100;
            zoomPosition = { x: -x, y: -y };
            updateImageTransform();
        });
        
        // Handle image load errors
        zoomImage.addEventListener('error', function() {
            alert('Failed to load image. Please check the URL and make sure the backend is running.');
        });
    </script>
</body>
</html>
