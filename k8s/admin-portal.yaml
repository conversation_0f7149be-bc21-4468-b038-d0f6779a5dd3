apiVersion: apps/v1
kind: Deployment
metadata:
  name: admin-portal
  namespace: jewelry-store
  labels:
    app: admin-portal
spec:
  replicas: 2
  selector:
    matchLabels:
      app: admin-portal
  template:
    metadata:
      labels:
        app: admin-portal
    spec:
      containers:
      - name: admin-portal
        image: asia-southeast1-docker.pkg.dev/emacs-464306/prod/anandjewels/admin-portal:2
        ports:
        - containerPort: 8080
        env:
        - name: VITE_API_URL
          value: "http://backend-service:8080/api/v1"
        - name: VITE_APP_NAME
          valueFrom:
            configMapKeyRef:
              name: admin-portal-config
              key: VITE_APP_NAME
        - name: VITE_DEBUG_MODE
          valueFrom:
            configMapKeyRef:
              name: admin-portal-config
              key: VITE_DEBUG_MODE
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: admin-portal-service
  namespace: jewelry-store
  labels:
    app: admin-portal
spec:
  selector:
    app: admin-portal
  ports:
  - port: 80
    targetPort: 8080
  type: ClusterIP
