apiVersion: v1
kind: ConfigMap
metadata:
  name: backend-config
  namespace: jewelry-store
data:
  PORT: "8080"
  GIN_MODE: "release"
  MINIO_BUCKET_NAME: "jewelry-images"
  MINIO_USE_SSL: "true"
  API_BASE_URL: "https://your-backend-domain.com"
  GOOGLE_REDIRECT_URL: "https://your-backend-domain.com/api/v1/auth/google/callback"
  # FRONTEND_URL: "https://your-frontend-domain.com"  # Optional - will auto-detect from Origin header
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: frontend-config
  namespace: jewelry-store
data:
  VITE_APP_NAME: "Anand Jewels"
  VITE_DEBUG_MODE: "false"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: admin-portal-config
  namespace: jewelry-store
data:
  VITE_APP_NAME: "Anand Jewels Admin"
  VITE_DEBUG_MODE: "false"
