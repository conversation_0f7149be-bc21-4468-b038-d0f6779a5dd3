apiVersion: v1
kind: Secret
metadata:
  name: backend-secrets
  namespace: jewelry-store
type: Opaque
data:
  # Base64 encoded values - replace with actual values
  # echo -n "your-value" | base64
  DATABASE_URL: cG9zdGdyZXNxbDovL3VzZXJuYW1lOnBhc3N3b3JkQGhvc3Q6cG9ydC9kYXRhYmFzZQ==
  #REDIS_URL: cmVkaXM6Ly9ob3N0OnBvcnQ=
  #MINIO_ENDPOINT: bWluaW8tc2VydmljZTo5MDAw
  #MINIO_ACCESS_KEY: bWluaW9hZG1pbg==
  #MINIO_SECRET_KEY: bWluaW9hZG1pbjEyMw==
  GOOGLE_CLIENT_ID: MjA3Mjc0Mzc2MDQxLXRlNW4zNDczNjdlNnZvbmZrdnA3YTJxY3RmZzE0NnFwLmFwcHMuZ29vZ2xldXNlcmNvbnRlbnQuY29t
  GOOGLE_CLIENT_SECRET: R09DU1BYLTVCaG54ZGkzOU5vdEVYVDJNVUs4c2kyN3RtWUc=
  JWT_SECRET: eW91ci1qd3Qtc2VjcmV0LWtleQ==
---
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secrets
  namespace: jewelry-store
type: Opaque
data:
  # Base64 encoded values
  POSTGRES_DB: amV3ZWxyeQ==
  POSTGRES_USER: amV3ZWxyeQ==
  POSTGRES_PASSWORD: amV3ZWxyeXBhc3M=
---
apiVersion: v1
kind: Secret
metadata:
  name: redis-secrets
  namespace: jewelry-store
type: Opaque
data:
  # Base64 encoded values
  REDIS_PASSWORD: cmVkaXNwYXNz
---
apiVersion: v1
kind: Secret
metadata:
  name: minio-secrets
  namespace: jewelry-store
type: Opaque
data:
  # Base64 encoded values
  MINIO_ROOT_USER: bWluaW9hZG1pbg==
  MINIO_ROOT_PASSWORD: bWluaW9hZG1pbjEyMw==
