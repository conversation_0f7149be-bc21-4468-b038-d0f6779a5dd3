# Anand Jewels - Customer Frontend

A modern, responsive customer shopping interface for the Anand Jewels e-commerce platform, built with React, TypeScript, and TailwindCSS to match the admin-portal's design system.

## Tech Stack

- **React 19** - UI library
- **TypeScript** - Type safety
- **Vite** - Build tool and dev server
- **TailwindCSS** - Utility-first CSS framework
- **TanStack Query** - Data fetching and caching
- **React Router** - Client-side routing
- **Axios** - HTTP client
- **React Hook Form** - Form handling
- **Heroicons** - Icon library

## Features

### Implemented
- ✅ Project foundation with matching admin-portal theme
- ✅ Responsive layout with header, footer, and navigation
- ✅ Shopping cart context with localStorage persistence
- ✅ Wishlist context with localStorage persistence
- ✅ Notification system
- ✅ Error boundary for error handling
- ✅ API client with proper error handling
- ✅ Home page with featured products and collections
- ✅ Basic page structure for all routes

### In Progress
- 🔄 Collection browsing interface
- 🔄 Product detail pages with image gallery
- 🔄 Shopping cart and checkout flow
- 🔄 Search functionality
- 🔄 Mobile optimization

### Planned
- ⏳ Product filtering and sorting
- ⏳ Order tracking
- ⏳ Customer order history
- ⏳ Image zoom and gallery
- ⏳ Inventory validation

## Project Structure

```
src/
├── components/
│   ├── common/          # Reusable components
│   ├── layout/          # Layout components
│   └── products/        # Product-specific components
├── contexts/            # React contexts
├── lib/                 # Utilities and API client
├── pages/              # Page components
├── types/              # TypeScript type definitions
└── hooks/              # Custom React hooks
```

## Development

### Prerequisites
- Node.js 18+
- npm or yarn

### Setup
1. Install dependencies:
   ```bash
   npm install
   ```

2. Create environment file:
   ```bash
   cp .env.example .env
   ```

3. Update environment variables:
   ```
   VITE_API_URL=http://localhost:8080/api/v1
   VITE_APP_NAME=Anand Jewels
   VITE_DEBUG_MODE=true
   ```

4. Start development server:
   ```bash
   npm run dev
   ```

The application will be available at `http://localhost:5173`

### Build for Production
```bash
npm run build
```

## Design System

The frontend uses the same design system as the admin-portal:

### Colors
- **Primary**: Blue tones for main actions and links
- **Gold**: Accent color for jewelry-specific elements
- **Gray**: Neutral colors for text and backgrounds

### Typography
- **Headings**: Playfair Display (serif) for elegance
- **Body**: Inter (sans-serif) for readability

### Components
- Consistent button styles (`btn-primary`, `btn-outline`, `btn-gold`)
- Card components with hover effects
- Form inputs with proper focus states
- Badge components for status indicators

## API Integration

The frontend integrates with the Go backend API:

- **Collections**: Browse and view collection details
- **Products**: Product listing, search, and details
- **Orders**: Order creation and tracking
- **Customers**: Customer information management

## State Management

- **Cart**: React Context with localStorage persistence
- **Wishlist**: React Context with localStorage persistence
- **Notifications**: React Context for toast messages
- **Server State**: TanStack Query for API data

## Responsive Design

The interface is fully responsive with:
- Mobile-first approach
- Breakpoints: sm (640px), md (768px), lg (1024px), xl (1280px)
- Touch-friendly interactions
- Optimized layouts for all screen sizes

## Performance

- Code splitting with React.lazy
- Image optimization and lazy loading
- Efficient re-renders with React.memo
- Optimized bundle size with Vite

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)

## Contributing

1. Follow the existing code style and patterns
2. Use TypeScript for all new components
3. Add proper error handling
4. Test on multiple screen sizes
5. Follow the established design system
