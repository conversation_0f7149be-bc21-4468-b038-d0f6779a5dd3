import React from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import {
  CheckCircleIcon,
  PhoneIcon,
  EnvelopeIcon,
  TruckIcon,
  PrinterIcon,
  ShareIcon
} from '@heroicons/react/24/outline';
import { getOrder } from '../lib/api';

const OrderConfirmationPage: React.FC = () => {
  const { orderId } = useParams<{ orderId: string }>();

  const { data: order, isLoading, error } = useQuery({
    queryKey: ['order', orderId],
    queryFn: () => getOrder(orderId!),
    enabled: !!orderId,
  });

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handlePrint = () => {
    window.print();
  };

  const handleShare = async () => {
    try {
      await navigator.share({
        title: `Order Confirmation - ${order?.order_number}`,
        text: `Your jewelry order has been confirmed! Order number: ${order?.order_number}`,
        url: window.location.href,
      });
    } catch (error) {
      // Fallback to copying URL
      await navigator.clipboard.writeText(window.location.href);
    }
  };

  if (error) {
    return (
      <div className="container-custom py-16">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Order Not Found</h1>
          <p className="text-gray-600 mb-8">
            The order you're looking for doesn't exist or you don't have permission to view it.
          </p>
          <Link to="/" className="btn-primary">
            Go Home
          </Link>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="container-custom py-8">
        <div className="animate-pulse">
          <div className="skeleton h-8 w-64 mb-6"></div>
          <div className="skeleton h-32 w-full mb-6"></div>
          <div className="skeleton h-64 w-full"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Success Header */}
      <div className="bg-green-50 border-b border-green-200">
        <div className="container-custom py-8">
          <div className="text-center">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-4">
              <CheckCircleIcon className="h-8 w-8 text-green-600" />
            </div>
            <h1 className="text-3xl font-bold font-serif text-gray-900 mb-2">
              Order Confirmed!
            </h1>
            <p className="text-lg text-gray-600 mb-4">
              Thank you for your order. We'll contact you shortly to confirm the details.
            </p>
            <div className="flex items-center justify-center space-x-4">
              <span className="text-sm text-gray-500">Order Number:</span>
              <span className="text-lg font-bold text-primary-600">{order?.order_number}</span>
            </div>
          </div>
        </div>
      </div>

      <div className="container-custom py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Next Steps */}
            <div className="card p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">What Happens Next?</h2>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-primary-600">1</span>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Order Confirmation Call</h3>
                    <p className="text-sm text-gray-600">
                      Our team will contact you within 2-4 hours to confirm your order details and discuss payment options.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-primary-600">2</span>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Payment Processing</h3>
                    <p className="text-sm text-gray-600">
                      Complete the payment through your preferred method (UPI, bank transfer, or cash on delivery).
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-primary-600">3</span>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Order Preparation</h3>
                    <p className="text-sm text-gray-600">
                      Your jewelry will be carefully prepared and packaged for delivery.
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <span className="text-sm font-medium text-primary-600">4</span>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Delivery</h3>
                    <p className="text-sm text-gray-600">
                      Your order will be delivered to your specified address with tracking information.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Order Details */}
            <div className="card p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-lg font-semibold text-gray-900">Order Details</h2>
                <div className="flex space-x-2">
                  <button
                    onClick={handlePrint}
                    className="btn-outline btn-sm flex items-center space-x-1"
                  >
                    <PrinterIcon className="h-4 w-4" />
                    <span>Print</span>
                  </button>
                  <button
                    onClick={handleShare}
                    className="btn-outline btn-sm flex items-center space-x-1"
                  >
                    <ShareIcon className="h-4 w-4" />
                    <span>Share</span>
                  </button>
                </div>
              </div>

              {/* Order Items */}
              <div className="space-y-4 mb-6">
                {order?.items.map((item) => (
                  <div key={item.id} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                    <div className="w-16 h-16 bg-gray-200 rounded-lg flex-shrink-0">
                      {/* Product image would go here */}
                      <div className="w-full h-full bg-gray-300 rounded-lg"></div>
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900">{item.product_name}</h3>
                      <p className="text-sm text-gray-600">SKU: {item.product_sku}</p>
                      <p className="text-sm text-gray-600">
                        Quantity: {item.quantity} × {formatPrice(item.unit_price)}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900">{formatPrice(item.total_price)}</p>
                    </div>
                  </div>
                ))}
              </div>

              {/* Order Summary */}
              <div className="border-t border-gray-200 pt-4">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Subtotal</span>
                    <span className="font-medium text-gray-900">{formatPrice(order?.subtotal || 0)}</span>
                  </div>
                  {order?.tax_amount && order.tax_amount > 0 && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Tax</span>
                      <span className="font-medium text-gray-900">{formatPrice(order.tax_amount)}</span>
                    </div>
                  )}
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Shipping</span>
                    <span className="font-medium text-green-600">Free</span>
                  </div>
                  <div className="border-t border-gray-200 pt-2">
                    <div className="flex justify-between">
                      <span className="text-base font-semibold text-gray-900">Total</span>
                      <span className="text-lg font-bold text-primary-600">{formatPrice(order?.total_amount || 0)}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Order Status */}
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Status</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Status</span>
                  <span className="badge-warning capitalize">{order?.status.replace('_', ' ')}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Payment</span>
                  <span className="badge-warning capitalize">{order?.payment_status.replace('_', ' ')}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Order Date</span>
                  <span className="text-sm font-medium text-gray-900">
                    {order?.created_at && formatDate(order.created_at)}
                  </span>
                </div>
              </div>
            </div>

            {/* Customer Information */}
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Information</h3>
              <div className="space-y-3">
                <div>
                  <span className="text-sm text-gray-600">Name</span>
                  <p className="font-medium text-gray-900">{order?.customer_name}</p>
                </div>
                <div>
                  <span className="text-sm text-gray-600">Phone</span>
                  <p className="font-medium text-gray-900">{order?.customer_phone}</p>
                </div>
                {order?.customer_email && (
                  <div>
                    <span className="text-sm text-gray-600">Email</span>
                    <p className="font-medium text-gray-900">{order.customer_email}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Shipping Address */}
            {order?.customer_address && (
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Shipping Address</h3>
                <div className="text-sm text-gray-600">
                  <p className="font-medium text-gray-900 mb-1">{order.customer_name}</p>
                  <p>{order.customer_address.street}</p>
                  <p>{order.customer_address.city}, {order.customer_address.state} {order.customer_address.postal_code}</p>
                  <p>{order.customer_address.country}</p>
                </div>
              </div>
            )}

            {/* Special Instructions */}
            {order?.special_instructions && (
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Special Instructions</h3>
                <p className="text-sm text-gray-600">{order.special_instructions}</p>
              </div>
            )}

            {/* Contact Information */}
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Need Help?</h3>
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <PhoneIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Call Us</p>
                    <a href="tel:+919876543210" className="text-sm text-primary-600 hover:text-primary-700">
                      +91 98765 43210
                    </a>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <EnvelopeIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Email Us</p>
                    <a href="mailto:<EMAIL>" className="text-sm text-primary-600 hover:text-primary-700">
                      <EMAIL>
                    </a>
                  </div>
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="space-y-3">
              <Link
                to={`/order-tracking/${order?.order_number}`}
                className="btn-primary w-full flex items-center justify-center space-x-2"
              >
                <TruckIcon className="h-4 w-4" />
                <span>Track Order</span>
              </Link>

              <Link
                to="/"
                className="btn-outline w-full flex items-center justify-center space-x-2"
              >
                <span>Continue Shopping</span>
              </Link>
            </div>
          </div>
        </div>

        {/* Additional Information */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-6 bg-white rounded-lg border border-gray-200">
            <TruckIcon className="h-8 w-8 text-primary-600 mx-auto mb-3" />
            <h3 className="font-medium text-gray-900 mb-2">Free Shipping</h3>
            <p className="text-sm text-gray-600">
              Complimentary shipping on all orders across India
            </p>
          </div>

          <div className="text-center p-6 bg-white rounded-lg border border-gray-200">
            <PhoneIcon className="h-8 w-8 text-primary-600 mx-auto mb-3" />
            <h3 className="font-medium text-gray-900 mb-2">Personal Service</h3>
            <p className="text-sm text-gray-600">
              Dedicated support team for all your jewelry needs
            </p>
          </div>

          <div className="text-center p-6 bg-white rounded-lg border border-gray-200">
            <CheckCircleIcon className="h-8 w-8 text-primary-600 mx-auto mb-3" />
            <h3 className="font-medium text-gray-900 mb-2">Quality Assured</h3>
            <p className="text-sm text-gray-600">
              Certified authentic jewelry with quality guarantee
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderConfirmationPage;
