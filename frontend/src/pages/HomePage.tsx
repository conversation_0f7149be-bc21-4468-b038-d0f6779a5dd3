import React from 'react';
import { Link } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getFeaturedProducts, getCollections } from '../lib/api';
import ProductGrid from '../components/products/ProductGrid';

const HomePage: React.FC = () => {
  const { data: featuredProducts, isLoading: productsLoading } = useQuery({
    queryKey: ['featured-products'],
    queryFn: () => getFeaturedProducts(8),
  });

  const { data: collectionsData, isLoading: collectionsLoading } = useQuery({
    queryKey: ['collections', { limit: 6, is_public: true }],
    queryFn: () => getCollections({ limit: 6, is_public: true }),
  });

  return (
    <div className="space-y-16">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-r from-gray-900 to-gray-700 text-white">
        <div className="container-custom py-24 lg:py-32">
          <div className="max-w-3xl">
            <h1 className="text-4xl lg:text-6xl font-bold font-serif mb-6">
              Exquisite Jewelry
              <span className="block text-gold-400">Crafted with Love</span>
            </h1>
            <p className="text-xl text-gray-300 mb-8">
              Discover our curated collection of handcrafted jewelry pieces, 
              each telling a unique story of elegance and craftsmanship.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Link to="/collections" className="btn-gold btn-lg">
                Explore Collections
              </Link>
              <Link to="/products" className="btn-outline btn-lg text-black border-white hover:bg-white hover:text-gray-900">
                View All Products
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section className="container-custom">
        <div className="text-center mb-12">
          <h2 className="text-3xl lg:text-4xl font-bold font-serif text-gray-900 mb-4">
            Featured Products
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Handpicked pieces that showcase the finest in jewelry craftsmanship
          </p>
        </div>

        <ProductGrid
          products={featuredProducts || []}
          isLoading={productsLoading}
          emptyMessage="No featured products available at the moment."
        />

        <div className="text-center mt-12">
          <Link to="/products" className="btn-primary btn-lg">
            View All Products
          </Link>
        </div>
      </section>

      {/* Collections */}
      <section className="bg-gray-100 py-16">
        <div className="container-custom">
          <div className="text-center mb-12">
            <h2 className="text-3xl lg:text-4xl font-bold font-serif text-gray-900 mb-4">
              Our Collections
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Curated collections for every occasion and style
            </p>
          </div>

          {collectionsLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {Array.from({ length: 6 }).map((_, index) => (
                <div key={index} className="card">
                  <div className="skeleton h-48 rounded-t-lg"></div>
                  <div className="p-6">
                    <div className="skeleton-text mb-2"></div>
                    <div className="skeleton-text w-3/4"></div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {collectionsData?.data.map((collection) => (
                <div key={collection.id} className="card-hover">
                  <Link to={`/collection/${collection.slug}`}>
                    <div className="aspect-video overflow-hidden rounded-t-lg">
                      <img
                        src={collection.cover_image_url || '/placeholder-collection.jpg'}
                        alt={collection.name}
                        className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {collection.name}
                      </h3>
                      {collection.description && (
                        <p className="text-gray-600 line-clamp-2">
                          {collection.description}
                        </p>
                      )}
                      <div className="mt-4 flex items-center justify-between">
                        <span className="text-sm text-gray-500">
                          {collection.product_count || 0} items
                        </span>
                        <span className="text-primary-600 font-medium">
                          View Collection →
                        </span>
                      </div>
                    </div>
                  </Link>
                </div>
              ))}
            </div>
          )}

          <div className="text-center mt-12">
            <Link to="/collections" className="btn-outline btn-lg">
              View All Collections
            </Link>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-primary-600 text-white py-16">
        <div className="container-custom text-center">
          <h2 className="text-3xl lg:text-4xl font-bold font-serif mb-4">
            Ready to Find Your Perfect Piece?
          </h2>
          <p className="text-xl text-primary-100 mb-8 max-w-2xl mx-auto">
            Browse our collections or get in touch with our experts for personalized recommendations.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/collections" className="btn-gold btn-lg">
              Browse Collections
            </Link>
            <Link to="/contact" className="btn-outline btn-lg text-black border-white hover:bg-white hover:text-primary-600">
              Contact Us
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
