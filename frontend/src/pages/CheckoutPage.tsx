import React, { useState, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  ShoppingBagIcon,
  CreditCardIcon,
  TruckIcon,
  ShieldCheckIcon,
  ArrowLeftIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { useCart } from '../contexts/CartContext';
import { useNotification } from '../contexts/NotificationContext';
import { createOrder } from '../lib/api';
import { customerSchema, formatPhoneNumber } from '../lib/validation';
import type { CustomerFormData } from '../types';

const CheckoutPage: React.FC = () => {
  const navigate = useNavigate();
  const { cart, clearCart } = useCart();
  const { addNotification } = useNotification();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [step, setStep] = useState<'information' | 'review' | 'confirmation'>('information');

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue
  } = useForm<CustomerFormData>({
    resolver: zodResolver(customerSchema),
    defaultValues: {
      address: {
        country: 'India'
      }
    }
  });

  // Redirect if cart is empty
  useEffect(() => {
    if (cart.total_items === 0) {
      addNotification({
        type: 'warning',
        message: 'Your cart is empty. Please add items before checkout.'
      });
      navigate('/');
    }
  }, [cart.total_items, navigate, addNotification]);

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatPhoneNumber(e.target.value);
    setValue('phone', formatted);
  };

  const validateInventory = async () => {
    // Check inventory for all items in cart
    const inventoryIssues = [];

    for (const item of cart.items) {
      if (item.product.availability !== 'available') {
        inventoryIssues.push({
          product: item.product,
          issue: 'not_available',
          message: `${item.product.name} is no longer available`
        });
      } else if (item.product.stock_quantity < item.quantity) {
        inventoryIssues.push({
          product: item.product,
          issue: 'insufficient_stock',
          message: `Only ${item.product.stock_quantity} units of ${item.product.name} are available (you have ${item.quantity} in cart)`
        });
      }
    }

    return inventoryIssues;
  };

  const onSubmit = async (data: CustomerFormData) => {
    if (step === 'information') {
      // Validate inventory before proceeding to review
      const inventoryIssues = await validateInventory();

      if (inventoryIssues.length > 0) {
        // Show inventory issues
        inventoryIssues.forEach(issue => {
          addNotification({
            type: 'warning',
            message: issue.message
          });
        });

        // Redirect to cart to resolve issues
        navigate('/cart');
        return;
      }

      setStep('review');
      return;
    }

    if (step === 'review') {
      setIsSubmitting(true);
      try {
        // Final inventory validation before order creation
        const inventoryIssues = await validateInventory();

        if (inventoryIssues.length > 0) {
          inventoryIssues.forEach(issue => {
            addNotification({
              type: 'error',
              message: issue.message
            });
          });

          // Go back to cart to resolve issues
          navigate('/cart');
          return;
        }

        const orderData = {
          customer: {
            name: data.name,
            phone: data.phone,
            email: data.email,
            address: data.address,
            special_instructions: data.special_instructions,
          },
          items: cart.items.map(item => ({
            product_id: item.product.id,
            quantity: item.quantity,
          })),
        };

        const order = await createOrder(orderData);

        // Clear cart and redirect to confirmation
        clearCart();
        navigate(`/order-confirmation/${order.id}`);

        addNotification({
          type: 'success',
          message: 'Order placed successfully! We will contact you shortly.'
        });
      } catch (error) {
        console.error('Order creation failed:', error);

        // Check if it's an inventory error from the backend
        if (error instanceof Error && error.message.includes('inventory')) {
          addNotification({
            type: 'error',
            message: 'Some items in your cart are no longer available. Please review your cart.'
          });
          navigate('/cart');
        } else {
          addNotification({
            type: 'error',
            message: 'Failed to place order. Please try again.'
          });
        }
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const goBack = () => {
    if (step === 'review') {
      setStep('information');
    } else {
      navigate('/cart');
    }
  };

  if (cart.total_items === 0) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="container-custom py-6">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold font-serif text-gray-900">Checkout</h1>
            <div className="flex items-center space-x-4">
              <div className={`flex items-center space-x-2 ${step === 'information' ? 'text-primary-600' : 'text-gray-400'}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step === 'information' ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-600'
                }`}>
                  1
                </div>
                <span className="text-sm font-medium">Information</span>
              </div>
              <div className="w-8 h-px bg-gray-300"></div>
              <div className={`flex items-center space-x-2 ${step === 'review' ? 'text-primary-600' : 'text-gray-400'}`}>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  step === 'review' ? 'bg-primary-600 text-white' : 'bg-gray-200 text-gray-600'
                }`}>
                  2
                </div>
                <span className="text-sm font-medium">Review</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container-custom py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
              {step === 'information' && (
                <>
                  {/* Customer Information */}
                  <div className="card p-6">
                    <h2 className="text-lg font-semibold text-gray-900 mb-6">Customer Information</h2>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="md:col-span-2">
                        <label className="form-label">Full Name *</label>
                        <input
                          type="text"
                          {...register('name')}
                          className={`form-input ${errors.name ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                          placeholder="Enter your full name"
                        />
                        {errors.name && (
                          <p className="form-error">{errors.name.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="form-label">Phone Number *</label>
                        <input
                          type="tel"
                          {...register('phone')}
                          onChange={handlePhoneChange}
                          className={`form-input ${errors.phone ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                          placeholder="+91 98765 43210"
                        />
                        {errors.phone && (
                          <p className="form-error">{errors.phone.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="form-label">Email Address</label>
                        <input
                          type="email"
                          {...register('email')}
                          className={`form-input ${errors.email ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                          placeholder="<EMAIL>"
                        />
                        {errors.email && (
                          <p className="form-error">{errors.email.message}</p>
                        )}
                        <p className="text-sm text-gray-500 mt-1">Optional - for order updates</p>
                      </div>
                    </div>
                  </div>

                  {/* Shipping Address */}
                  <div className="card p-6">
                    <h2 className="text-lg font-semibold text-gray-900 mb-6">Shipping Address</h2>

                    <div className="space-y-6">
                      <div>
                        <label className="form-label">Street Address *</label>
                        <input
                          type="text"
                          {...register('address.street')}
                          className={`form-input ${errors.address?.street ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                          placeholder="House number, street name, area"
                        />
                        {errors.address?.street && (
                          <p className="form-error">{errors.address.street.message}</p>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="form-label">City *</label>
                          <input
                            type="text"
                            {...register('address.city')}
                            className={`form-input ${errors.address?.city ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                            placeholder="City"
                          />
                          {errors.address?.city && (
                            <p className="form-error">{errors.address.city.message}</p>
                          )}
                        </div>

                        <div>
                          <label className="form-label">State *</label>
                          <input
                            type="text"
                            {...register('address.state')}
                            className={`form-input ${errors.address?.state ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                            placeholder="State"
                          />
                          {errors.address?.state && (
                            <p className="form-error">{errors.address.state.message}</p>
                          )}
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="form-label">Postal Code *</label>
                          <input
                            type="text"
                            {...register('address.postal_code')}
                            className={`form-input ${errors.address?.postal_code ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                            placeholder="400001"
                          />
                          {errors.address?.postal_code && (
                            <p className="form-error">{errors.address.postal_code.message}</p>
                          )}
                        </div>

                        <div>
                          <label className="form-label">Country *</label>
                          <input
                            type="text"
                            {...register('address.country')}
                            className="form-input bg-gray-50"
                            value="India"
                            readOnly
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Special Instructions */}
                  <div className="card p-6">
                    <h2 className="text-lg font-semibold text-gray-900 mb-6">Special Instructions</h2>
                    <div>
                      <label className="form-label">Additional Notes</label>
                      <textarea
                        {...register('special_instructions')}
                        rows={4}
                        className={`form-input ${errors.special_instructions ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : ''}`}
                        placeholder="Any special delivery instructions or preferences..."
                      />
                      {errors.special_instructions && (
                        <p className="form-error">{errors.special_instructions.message}</p>
                      )}
                      <p className="text-sm text-gray-500 mt-1">Optional - maximum 500 characters</p>
                    </div>
                  </div>
                </>
              )}

              {step === 'review' && (
                <>
                  {/* Order Review */}
                  <div className="card p-6">
                    <h2 className="text-lg font-semibold text-gray-900 mb-6">Review Your Order</h2>

                    {/* Customer Information Review */}
                    <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                      <h3 className="font-medium text-gray-900 mb-3">Customer Information</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">Name:</span>
                          <span className="ml-2 font-medium">{watch('name')}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">Phone:</span>
                          <span className="ml-2 font-medium">{watch('phone')}</span>
                        </div>
                        {watch('email') && (
                          <div>
                            <span className="text-gray-600">Email:</span>
                            <span className="ml-2 font-medium">{watch('email')}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Shipping Address Review */}
                    {watch('address') && (
                      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                        <h3 className="font-medium text-gray-900 mb-3">Shipping Address</h3>
                        <div className="text-sm text-gray-600">
                          <p>{watch('address.street')}</p>
                          <p>{watch('address.city')}, {watch('address.state')} {watch('address.postal_code')}</p>
                          <p>{watch('address.country')}</p>
                        </div>
                      </div>
                    )}

                    {/* Special Instructions Review */}
                    {watch('special_instructions') && (
                      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                        <h3 className="font-medium text-gray-900 mb-3">Special Instructions</h3>
                        <p className="text-sm text-gray-600">{watch('special_instructions')}</p>
                      </div>
                    )}

                    {/* Payment Information */}
                    <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                      <div className="flex items-start space-x-3">
                        <ExclamationTriangleIcon className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                        <div>
                          <h3 className="font-medium text-blue-900 mb-2">Payment Process</h3>
                          <p className="text-sm text-blue-800">
                            After placing your order, our team will contact you via phone to confirm
                            the details and arrange payment. We accept various payment methods including
                            bank transfer, UPI, and cash on delivery.
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              )}

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4">
                <button
                  type="button"
                  onClick={goBack}
                  className="btn-outline flex items-center justify-center space-x-2"
                >
                  <ArrowLeftIcon className="h-4 w-4" />
                  <span>Back</span>
                </button>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="btn-primary flex-1 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>Processing...</span>
                    </>
                  ) : step === 'information' ? (
                    <>
                      <span>Continue to Review</span>
                    </>
                  ) : (
                    <>
                      <ShoppingBagIcon className="h-4 w-4" />
                      <span>Place Order</span>
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>

          {/* Order Summary Sidebar */}
          <div className="lg:col-span-1">
            <div className="card p-6 sticky top-8">
              <h2 className="text-lg font-semibold text-gray-900 mb-6">Order Summary</h2>

              {/* Inventory Warnings */}
              {cart.items.some(item =>
                item.product.availability !== 'available' ||
                item.product.stock_quantity < item.quantity
              ) && (
                <div className="mb-6 p-4 bg-amber-50 border border-amber-200 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <ExclamationTriangleIcon className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                    <div>
                      <h3 className="font-medium text-amber-900 mb-2">Inventory Notice</h3>
                      <div className="space-y-1 text-sm text-amber-800">
                        {cart.items.map((item) => {
                          if (item.product.availability !== 'available') {
                            return (
                              <p key={item.product.id}>
                                • {item.product.name} is no longer available
                              </p>
                            );
                          } else if (item.product.stock_quantity < item.quantity) {
                            return (
                              <p key={item.product.id}>
                                • Only {item.product.stock_quantity} units of {item.product.name} available
                                (you have {item.quantity} in cart)
                              </p>
                            );
                          }
                          return null;
                        }).filter(Boolean)}
                      </div>
                      <p className="text-sm text-amber-800 mt-2">
                        Please update your cart before proceeding with checkout.
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Cart Items */}
              <div className="space-y-4 mb-6">
                {cart.items.map((item) => (
                  <div key={item.product.id} className="flex items-center space-x-3">
                    <div className="w-16 h-16 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                      <img
                        src={item.product.images?.[0]?.thumbnail_url || item.product.images?.[0]?.image_url || 'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?w=100&h=100&fit=crop&crop=center'}
                        alt={item.product.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-sm font-medium text-gray-900 line-clamp-2">
                        {item.product.name}
                      </h3>
                      <p className="text-sm text-gray-500">
                        Qty: {item.quantity} × {formatPrice(item.product.price)}
                      </p>
                      {/* Stock warning for individual items */}
                      {item.product.availability !== 'available' && (
                        <p className="text-xs text-red-600 mt-1">Not available</p>
                      )}
                      {item.product.availability === 'available' && item.product.stock_quantity < item.quantity && (
                        <p className="text-xs text-amber-600 mt-1">
                          Limited stock ({item.product.stock_quantity} available)
                        </p>
                      )}
                    </div>
                    <div className="text-sm font-medium text-gray-900">
                      {formatPrice(item.product.price * item.quantity)}
                    </div>
                  </div>
                ))}
              </div>

              {/* Order Totals */}
              <div className="border-t border-gray-200 pt-4 space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Subtotal ({cart.total_items} items)</span>
                  <span className="font-medium text-gray-900">{formatPrice(cart.subtotal)}</span>
                </div>

                {cart.tax_amount > 0 && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Tax</span>
                    <span className="font-medium text-gray-900">{formatPrice(cart.tax_amount)}</span>
                  </div>
                )}

                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Shipping</span>
                  <span className="font-medium text-green-600">Free</span>
                </div>

                <div className="border-t border-gray-200 pt-3">
                  <div className="flex justify-between">
                    <span className="text-base font-semibold text-gray-900">Total</span>
                    <span className="text-lg font-bold text-primary-600">{formatPrice(cart.total_amount)}</span>
                  </div>
                </div>
              </div>

              {/* Security Features */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <div className="space-y-3">
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <ShieldCheckIcon className="h-4 w-4 text-green-500" />
                    <span>Secure checkout</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <TruckIcon className="h-4 w-4 text-green-500" />
                    <span>Free shipping</span>
                  </div>
                  <div className="flex items-center space-x-2 text-sm text-gray-600">
                    <CreditCardIcon className="h-4 w-4 text-green-500" />
                    <span>Multiple payment options</span>
                  </div>
                </div>
              </div>

              {/* Continue Shopping */}
              <div className="mt-6 pt-6 border-t border-gray-200">
                <Link
                  to="/"
                  className="text-sm text-primary-600 hover:text-primary-700 font-medium"
                >
                  ← Continue Shopping
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;
