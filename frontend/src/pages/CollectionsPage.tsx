import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { EyeIcon, CalendarIcon } from '@heroicons/react/24/outline';
import { getCollections } from '../lib/api';
import SearchBar from '../components/common/SearchBar';
import type { FilterOptions } from '../types';

const CollectionsPage: React.FC = () => {
  const [filters, setFilters] = useState<FilterOptions>({
    page: 1,
    limit: 12,
    is_public: true,
    search: '',
  });

  const { data, isLoading, error } = useQuery({
    queryKey: ['collections', filters],
    queryFn: () => getCollections(filters),
  });

  const handleSearch = (query: string) => {
    setFilters(prev => ({ ...prev, search: query, page: 1 }));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (error) {
    return (
      <div className="container-custom py-16">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Error Loading Collections</h1>
          <p className="text-gray-600 mb-8">
            We're having trouble loading the collections. Please try again later.
          </p>
          <Link to="/" className="btn-primary">
            Go Home
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="container-custom py-12">
          <div className="text-center">
            <h1 className="text-4xl font-bold font-serif text-gray-900 mb-4">
              Our Collections
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Discover our curated jewelry collections, each carefully crafted to tell a unique story
            </p>
          </div>
        </div>
      </div>

      <div className="container-custom py-8">
        {/* Search and Filters */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            <div className="flex-1 max-w-md">
              <SearchBar 
                onSearch={handleSearch}
                placeholder="Search collections..."
              />
            </div>
            
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <span>
                {data?.total || 0} {data?.total === 1 ? 'collection' : 'collections'}
              </span>
            </div>
          </div>
        </div>

        {/* Collections Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="card">
                <div className="skeleton h-48 rounded-t-lg"></div>
                <div className="p-6 space-y-2">
                  <div className="skeleton-text"></div>
                  <div className="skeleton-text w-3/4"></div>
                  <div className="skeleton-text w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        ) : data?.data.length === 0 ? (
          <div className="text-center py-16">
            <div className="text-gray-400 mb-4">
              <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Collections Found</h3>
            <p className="text-gray-500">
              {filters.search 
                ? "No collections match your search criteria. Try adjusting your search terms."
                : "No collections are available at the moment."
              }
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {data?.data.map((collection) => (
              <div key={collection.id} className="card-hover group">
                <Link to={`/collection/${collection.slug}`}>
                  {/* Collection Image */}
                  <div className="aspect-video overflow-hidden rounded-t-lg bg-gray-100">
                    <img
                      src={collection.cover_image_url || 'https://images.unsplash.com/photo-1506630448388-4e683c67ddb0?w=600&h=400&fit=crop&crop=center'}
                      alt={collection.name}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      loading="lazy"
                    />
                  </div>

                  {/* Collection Info */}
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors">
                      {collection.name}
                    </h3>
                    
                    {collection.description && (
                      <p className="text-gray-600 mb-4 line-clamp-2">
                        {collection.description}
                      </p>
                    )}

                    {/* Collection Stats */}
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-1">
                          <EyeIcon className="h-4 w-4" />
                          <span>{collection.view_count}</span>
                        </div>
                        
                        <div className="flex items-center space-x-1">
                          <CalendarIcon className="h-4 w-4" />
                          <span>{formatDate(collection.created_at)}</span>
                        </div>
                      </div>
                    </div>

                    {/* Product Count and CTA */}
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">
                        {collection.product_count || 0} {collection.product_count === 1 ? 'item' : 'items'}
                      </span>
                      <span className="text-primary-600 font-medium group-hover:text-primary-700 transition-colors">
                        View Collection →
                      </span>
                    </div>

                    {/* Expiry Warning */}
                    {collection.expires_at && (
                      <div className="mt-3 p-2 bg-amber-50 border border-amber-200 rounded text-sm text-amber-800">
                        <div className="flex items-center space-x-1">
                          <CalendarIcon className="h-4 w-4" />
                          <span>Expires {formatDate(collection.expires_at)}</span>
                        </div>
                      </div>
                    )}
                  </div>
                </Link>
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {data && data.total_pages > 1 && (
          <div className="mt-12 flex justify-center">
            <nav className="flex items-center space-x-2">
              {/* Previous Button */}
              <button
                onClick={() => setFilters(prev => ({ ...prev, page: Math.max(1, prev.page! - 1) }))}
                disabled={filters.page === 1}
                className="btn-outline disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Previous
              </button>

              {/* Page Numbers */}
              {Array.from({ length: Math.min(5, data.total_pages) }, (_, i) => {
                const page = i + 1;
                return (
                  <button
                    key={page}
                    onClick={() => setFilters(prev => ({ ...prev, page }))}
                    className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                      filters.page === page
                        ? 'bg-primary-600 text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    {page}
                  </button>
                );
              })}

              {/* Next Button */}
              <button
                onClick={() => setFilters(prev => ({ ...prev, page: Math.min(data.total_pages, prev.page! + 1) }))}
                disabled={filters.page === data.total_pages}
                className="btn-outline disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            </nav>
          </div>
        )}
      </div>
    </div>
  );
};

export default CollectionsPage;
