import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import {
  TruckIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  CalendarIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import { getOrderByNumber } from '../lib/api';

const OrderTrackingPage: React.FC = () => {
  const { orderNumber } = useParams<{ orderNumber: string }>();
  const [searchOrderNumber, setSearchOrderNumber] = useState(orderNumber || '');

  const { data: order, isLoading, error, refetch } = useQuery({
    queryKey: ['order-tracking', orderNumber],
    queryFn: () => getOrderByNumber(orderNumber!),
    enabled: !!orderNumber,
  });

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(price);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'pending':
        return {
          icon: <ClockIcon className="h-6 w-6 text-yellow-500" />,
          color: 'text-yellow-600',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          title: 'Order Pending',
          description: 'Your order is being reviewed and will be confirmed shortly.'
        };
      case 'confirmed':
        return {
          icon: <CheckCircleIcon className="h-6 w-6 text-blue-500" />,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          title: 'Order Confirmed',
          description: 'Your order has been confirmed and is being prepared.'
        };
      case 'processing':
        return {
          icon: <ClockIcon className="h-6 w-6 text-purple-500" />,
          color: 'text-purple-600',
          bgColor: 'bg-purple-50',
          borderColor: 'border-purple-200',
          title: 'Processing',
          description: 'Your jewelry is being carefully prepared for shipment.'
        };
      case 'shipped':
        return {
          icon: <TruckIcon className="h-6 w-6 text-blue-500" />,
          color: 'text-blue-600',
          bgColor: 'bg-blue-50',
          borderColor: 'border-blue-200',
          title: 'Shipped',
          description: 'Your order is on its way to you!'
        };
      case 'delivered':
        return {
          icon: <CheckCircleIcon className="h-6 w-6 text-green-500" />,
          color: 'text-green-600',
          bgColor: 'bg-green-50',
          borderColor: 'border-green-200',
          title: 'Delivered',
          description: 'Your order has been successfully delivered.'
        };
      case 'cancelled':
        return {
          icon: <XCircleIcon className="h-6 w-6 text-red-500" />,
          color: 'text-red-600',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          title: 'Cancelled',
          description: 'This order has been cancelled.'
        };
      default:
        return {
          icon: <ClockIcon className="h-6 w-6 text-gray-500" />,
          color: 'text-gray-600',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          title: 'Unknown Status',
          description: 'Order status is being updated.'
        };
    }
  };

  const getOrderTimeline = () => {
    if (!order) return [];

    const timeline = [
      {
        status: 'pending',
        title: 'Order Placed',
        date: order.created_at,
        completed: true
      },
      {
        status: 'confirmed',
        title: 'Order Confirmed',
        date: order.confirmed_at,
        completed: ['confirmed', 'processing', 'shipped', 'delivered'].includes(order.status)
      },
      {
        status: 'processing',
        title: 'Processing',
        date: order.status === 'processing' ? new Date().toISOString() : null,
        completed: ['processing', 'shipped', 'delivered'].includes(order.status)
      },
      {
        status: 'shipped',
        title: 'Shipped',
        date: order.shipped_at,
        completed: ['shipped', 'delivered'].includes(order.status)
      },
      {
        status: 'delivered',
        title: 'Delivered',
        date: order.delivered_at,
        completed: order.status === 'delivered'
      }
    ];

    return timeline.filter(item => item.status !== 'cancelled' || order.status === 'cancelled');
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchOrderNumber.trim()) {
      window.location.href = `/order-tracking/${searchOrderNumber.trim()}`;
    }
  };

  const statusInfo = order ? getStatusInfo(order.status) : null;
  const timeline = getOrderTimeline();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="container-custom py-8">
          <div className="text-center">
            <h1 className="text-3xl font-bold font-serif text-gray-900 mb-4">
              Track Your Order
            </h1>
            <p className="text-lg text-gray-600">
              Enter your order number to check the status of your jewelry order
            </p>
          </div>
        </div>
      </div>

      <div className="container-custom py-8">
        {/* Search Form */}
        <div className="max-w-md mx-auto mb-8">
          <form onSubmit={handleSearch} className="flex gap-2">
            <div className="flex-1">
              <input
                type="text"
                value={searchOrderNumber}
                onChange={(e) => setSearchOrderNumber(e.target.value)}
                placeholder="Enter order number (e.g., ORD-2024-001)"
                className="form-input w-full"
              />
            </div>
            <button
              type="submit"
              className="btn-primary flex items-center space-x-2"
            >
              <MagnifyingGlassIcon className="h-4 w-4" />
              <span>Track</span>
            </button>
          </form>
        </div>

        {/* Loading State */}
        {isLoading && (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading order information...</p>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="max-w-2xl mx-auto">
            <div className="card p-8 text-center">
              <XCircleIcon className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Order Not Found</h2>
              <p className="text-gray-600 mb-6">
                We couldn't find an order with the number "{orderNumber}".
                Please check the order number and try again.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={() => refetch()}
                  className="btn-primary"
                >
                  Try Again
                </button>
                <Link to="/" className="btn-outline">
                  Go Home
                </Link>
              </div>
            </div>
          </div>
        )}

        {/* Order Found */}
        {order && statusInfo && (
          <div className="max-w-4xl mx-auto space-y-8">
            {/* Order Status Card */}
            <div className={`card p-6 border-2 ${statusInfo.borderColor} ${statusInfo.bgColor}`}>
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  {statusInfo.icon}
                </div>
                <div className="flex-1">
                  <h2 className={`text-xl font-semibold ${statusInfo.color}`}>
                    {statusInfo.title}
                  </h2>
                  <p className="text-gray-600 mt-1">
                    {statusInfo.description}
                  </p>
                  <p className="text-sm text-gray-500 mt-2">
                    Order #{order.order_number} • Placed on {formatDate(order.created_at)}
                  </p>
                </div>
              </div>
            </div>

            {/* Order Timeline */}
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">Order Progress</h3>
              <div className="space-y-6">
                {timeline.map((step, index) => (
                  <div key={step.status} className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                        step.completed
                          ? 'bg-green-100 text-green-600'
                          : 'bg-gray-100 text-gray-400'
                      }`}>
                        {step.completed ? (
                          <CheckCircleIcon className="h-6 w-6" />
                        ) : (
                          <ClockIcon className="h-6 w-6" />
                        )}
                      </div>
                      {index < timeline.length - 1 && (
                        <div className={`w-0.5 h-8 mx-auto mt-2 ${
                          step.completed ? 'bg-green-200' : 'bg-gray-200'
                        }`} />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className={`font-medium ${
                        step.completed ? 'text-gray-900' : 'text-gray-500'
                      }`}>
                        {step.title}
                      </h4>
                      {step.date && (
                        <p className="text-sm text-gray-500 mt-1">
                          {formatDate(step.date)}
                        </p>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Order Details */}
              <div className="card p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Details</h3>
                <div className="space-y-4">
                  {order.items.map((item) => (
                    <div key={item.id} className="flex items-center space-x-4 p-3 bg-gray-50 rounded-lg">
                      <div className="w-12 h-12 bg-gray-200 rounded-lg flex-shrink-0">
                        {/* Product image placeholder */}
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{item.product_name}</h4>
                        <p className="text-sm text-gray-600">
                          Qty: {item.quantity} × {formatPrice(item.unit_price)}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-gray-900">{formatPrice(item.total_price)}</p>
                      </div>
                    </div>
                  ))}

                  <div className="border-t border-gray-200 pt-4">
                    <div className="flex justify-between">
                      <span className="font-semibold text-gray-900">Total</span>
                      <span className="font-bold text-primary-600">{formatPrice(order.total_amount)}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Customer & Shipping Info */}
              <div className="space-y-6">
                {/* Customer Information */}
                <div className="card p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Information</h3>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-medium text-gray-600">
                          {order.customer_name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{order.customer_name}</p>
                        <p className="text-sm text-gray-600">{order.customer_phone}</p>
                      </div>
                    </div>
                    {order.customer_email && (
                      <div className="flex items-center space-x-3">
                        <EnvelopeIcon className="h-5 w-5 text-gray-400" />
                        <span className="text-sm text-gray-600">{order.customer_email}</span>
                      </div>
                    )}
                  </div>
                </div>

                {/* Shipping Address */}
                {order.customer_address && (
                  <div className="card p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Shipping Address</h3>
                    <div className="flex items-start space-x-3">
                      <MapPinIcon className="h-5 w-5 text-gray-400 mt-0.5" />
                      <div className="text-sm text-gray-600">
                        <p className="font-medium text-gray-900">{order.customer_name}</p>
                        <p>{order.customer_address.street}</p>
                        <p>{order.customer_address.city}, {order.customer_address.state} {order.customer_address.postal_code}</p>
                        <p>{order.customer_address.country}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Contact Support */}
            <div className="card p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Need Help?</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="flex items-center space-x-3">
                  <PhoneIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="font-medium text-gray-900">Call Us</p>
                    <a href="tel:+919876543210" className="text-primary-600 hover:text-primary-700">
                      +91 98765 43210
                    </a>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <EnvelopeIcon className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="font-medium text-gray-900">Email Us</p>
                    <a href="mailto:<EMAIL>" className="text-primary-600 hover:text-primary-700">
                      <EMAIL>
                    </a>
                  </div>
                </div>
              </div>
              <div className="mt-4 p-4 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>Order Questions?</strong> Our customer service team is available
                  Monday-Saturday, 10 AM - 7 PM IST to help with any questions about your order.
                </p>
              </div>
            </div>

            {/* Actions */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/" className="btn-primary">
                Continue Shopping
              </Link>
              <Link to="/collections" className="btn-outline">
                Browse Collections
              </Link>
            </div>
          </div>
        )}

        {/* No Order Number Provided */}
        {!orderNumber && !isLoading && !error && (
          <div className="max-w-2xl mx-auto text-center">
            <TruckIcon className="h-16 w-16 text-gray-300 mx-auto mb-6" />
            <h2 className="text-2xl font-bold text-gray-900 mb-4">Track Your Order</h2>
            <p className="text-gray-600 mb-8">
              Enter your order number above to track the status of your jewelry order.
              You can find your order number in the confirmation email we sent you.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
              <div className="p-4 bg-white rounded-lg border border-gray-200">
                <CalendarIcon className="h-6 w-6 text-primary-600 mb-2" />
                <h3 className="font-medium text-gray-900 mb-1">Order Confirmation</h3>
                <p className="text-sm text-gray-600">
                  We'll call you within 2-4 hours to confirm your order details
                </p>
              </div>
              <div className="p-4 bg-white rounded-lg border border-gray-200">
                <TruckIcon className="h-6 w-6 text-primary-600 mb-2" />
                <h3 className="font-medium text-gray-900 mb-1">Free Delivery</h3>
                <p className="text-sm text-gray-600">
                  Complimentary shipping on all orders across India
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderTrackingPage;
