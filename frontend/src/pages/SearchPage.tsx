import React from 'react';
import { useSearchParams } from 'react-router-dom';

const SearchPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const query = searchParams.get('q') || '';

  return (
    <div className="container-custom py-8">
      <h1 className="text-3xl font-bold font-serif text-gray-900 mb-8">
        Search Results
      </h1>
      <p className="text-gray-600 mb-4">
        Searching for: "{query}"
      </p>
      <p className="text-gray-600">
        Search page implementation coming soon...
      </p>
    </div>
  );
};

export default SearchPage;
