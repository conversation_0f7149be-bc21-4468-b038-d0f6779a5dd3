import React from 'react';
import ProductCard from './ProductCard';
import type { Product } from '../../types';

interface ProductGridProps {
  products: Product[];
  isLoading?: boolean;
  showAddToCart?: boolean;
  emptyMessage?: string;
  className?: string;
}

const ProductGrid: React.FC<ProductGridProps> = ({
  products,
  isLoading = false,
  showAddToCart = true,
  emptyMessage = "No products found",
  className = ""
}) => {
  if (isLoading) {
    return (
      <div className={`product-grid ${className}`}>
        {Array.from({ length: 8 }).map((_, index) => (
          <div key={index} className="card">
            <div className="skeleton-image"></div>
            <div className="p-4 space-y-2">
              <div className="skeleton-text"></div>
              <div className="skeleton-text w-1/2"></div>
              <div className="skeleton-text w-3/4"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (products.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-400 mb-4">
          <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H6a1 1 0 00-1 1v1m16 0V4a1 1 0 00-1-1H6a1 1 0 00-1 1v1" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Products Found</h3>
        <p className="text-gray-500">{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div className={`product-grid ${className}`}>
      {products.map((product) => (
        <ProductCard
          key={product.id}
          product={product}
          showAddToCart={showAddToCart}
        />
      ))}
    </div>
  );
};

export default ProductGrid;
