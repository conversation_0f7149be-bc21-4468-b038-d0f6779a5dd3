import React from 'react';
import { FunnelIcon, XMarkIcon } from '@heroicons/react/24/outline';
import type { FilterOptions } from '../../types';

interface ProductFiltersProps {
  filters: FilterOptions;
  onFiltersChange: (filters: Partial<FilterOptions>) => void;
  onClearFilters: () => void;
  isOpen: boolean;
  onToggle: () => void;
}

const ProductFilters: React.FC<ProductFiltersProps> = ({
  filters,
  onFiltersChange,
  onClearFilters,
  isOpen,
  onToggle
}) => {
  const categories = [
    { value: 'rings', label: 'Rings' },
    { value: 'necklaces', label: 'Necklaces' },
    { value: 'earrings', label: 'Earrings' },
    { value: 'bracelets', label: 'Bracelets' },
    { value: 'pendants', label: 'Pendants' },
    { value: 'bangles', label: 'Bangles' },
  ];

  const materials = [
    { value: 'gold', label: 'Gold' },
    { value: 'silver', label: 'Silver' },
    { value: 'platinum', label: 'Platinum' },
    { value: 'diamond', label: 'Diamond' },
    { value: 'pearl', label: 'Pearl' },
  ];

  const priceRanges = [
    { min: 0, max: 10000, label: 'Under ₹10,000' },
    { min: 10000, max: 25000, label: '₹10,000 - ₹25,000' },
    { min: 25000, max: 50000, label: '₹25,000 - ₹50,000' },
    { min: 50000, max: 100000, label: '₹50,000 - ₹1,00,000' },
    { min: 100000, max: undefined, label: 'Above ₹1,00,000' },
  ];

  const sortOptions = [
    { value: 'name:asc', label: 'Name (A-Z)' },
    { value: 'name:desc', label: 'Name (Z-A)' },
    { value: 'price:asc', label: 'Price (Low to High)' },
    { value: 'price:desc', label: 'Price (High to Low)' },
    { value: 'created_at:desc', label: 'Newest First' },
    { value: 'created_at:asc', label: 'Oldest First' },
  ];

  const handlePriceRangeChange = (min: number, max?: number) => {
    onFiltersChange({
      price_min: min,
      price_max: max,
    });
  };

  const handleSortChange = (sortValue: string) => {
    const [sortBy, sortOrder] = sortValue.split(':');
    onFiltersChange({
      sort_by: sortBy as any,
      sort_order: sortOrder as 'asc' | 'desc',
    });
  };

  const hasActiveFilters = !!(
    filters.category ||
    filters.material ||
    filters.price_min ||
    filters.price_max ||
    filters.sort_by
  );

  return (
    <div className="space-y-4">
      {/* Filter Toggle Button */}
      <div className="flex items-center justify-between">
        <button
          onClick={onToggle}
          className="btn-outline flex items-center space-x-2"
        >
          <FunnelIcon className="h-4 w-4" />
          <span>Filters</span>
          {hasActiveFilters && (
            <span className="bg-primary-600 text-white text-xs rounded-full px-2 py-0.5">
              Active
            </span>
          )}
        </button>

        {hasActiveFilters && (
          <button
            onClick={onClearFilters}
            className="text-sm text-gray-600 hover:text-gray-900 flex items-center space-x-1"
          >
            <XMarkIcon className="h-4 w-4" />
            <span>Clear All</span>
          </button>
        )}
      </div>

      {/* Filter Panel */}
      {isOpen && (
        <div className="card p-6 space-y-6">
          {/* Sort */}
          <div>
            <label className="form-label">Sort By</label>
            <select
              value={filters.sort_by && filters.sort_order ? `${filters.sort_by}:${filters.sort_order}` : ''}
              onChange={(e) => handleSortChange(e.target.value)}
              className="form-input"
            >
              <option value="">Default</option>
              {sortOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>

          {/* Category */}
          <div>
            <label className="form-label">Category</label>
            <select
              value={filters.category || ''}
              onChange={(e) => onFiltersChange({ category: e.target.value || undefined })}
              className="form-input"
            >
              <option value="">All Categories</option>
              {categories.map((category) => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>
          </div>

          {/* Material */}
          <div>
            <label className="form-label">Material</label>
            <select
              value={filters.material || ''}
              onChange={(e) => onFiltersChange({ material: e.target.value || undefined })}
              className="form-input"
            >
              <option value="">All Materials</option>
              {materials.map((material) => (
                <option key={material.value} value={material.value}>
                  {material.label}
                </option>
              ))}
            </select>
          </div>

          {/* Price Range */}
          <div>
            <label className="form-label">Price Range</label>
            <div className="space-y-2">
              {priceRanges.map((range, index) => (
                <label key={index} className="flex items-center">
                  <input
                    type="radio"
                    name="priceRange"
                    checked={filters.price_min === range.min && filters.price_max === range.max}
                    onChange={() => handlePriceRangeChange(range.min, range.max)}
                    className="mr-2"
                  />
                  <span className="text-sm">{range.label}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Custom Price Range */}
          <div>
            <label className="form-label">Custom Price Range</label>
            <div className="grid grid-cols-2 gap-2">
              <input
                type="number"
                placeholder="Min price"
                value={filters.price_min || ''}
                onChange={(e) => onFiltersChange({ 
                  price_min: e.target.value ? Number(e.target.value) : undefined 
                })}
                className="form-input"
              />
              <input
                type="number"
                placeholder="Max price"
                value={filters.price_max || ''}
                onChange={(e) => onFiltersChange({ 
                  price_max: e.target.value ? Number(e.target.value) : undefined 
                })}
                className="form-input"
              />
            </div>
          </div>

          {/* Availability */}
          <div>
            <label className="form-label">Availability</label>
            <select
              value={filters.availability || ''}
              onChange={(e) => onFiltersChange({ availability: e.target.value || undefined })}
              className="form-input"
            >
              <option value="">All Items</option>
              <option value="available">Available</option>
              <option value="out_of_stock">Out of Stock</option>
            </select>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductFilters;
