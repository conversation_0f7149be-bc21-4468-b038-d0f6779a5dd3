import { z } from 'zod';

// Customer information validation schema
export const customerSchema = z.object({
  name: z.string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must be less than 100 characters'),
  
  email: z.string()
    .email('Please enter a valid email address')
    .optional()
    .or(z.literal('')),
  
  phone: z.string()
    .min(10, 'Phone number must be at least 10 digits')
    .max(15, 'Phone number must be less than 15 digits')
    .regex(/^[+]?[\d\s\-\(\)]+$/, 'Please enter a valid phone number'),
  
  address: z.object({
    street: z.string()
      .min(5, 'Street address must be at least 5 characters')
      .max(200, 'Street address must be less than 200 characters'),
    
    city: z.string()
      .min(2, 'City must be at least 2 characters')
      .max(50, 'City must be less than 50 characters'),
    
    state: z.string()
      .min(2, 'State must be at least 2 characters')
      .max(50, 'State must be less than 50 characters'),
    
    country: z.string()
      .min(2, 'Country must be at least 2 characters')
      .max(50, 'Country must be less than 50 characters')
      .default('India')
      .optional(),
    
    postal_code: z.string()
      .min(5, 'Postal code must be at least 5 characters')
      .max(10, 'Postal code must be less than 10 characters')
      .regex(/^[\d\-\s]+$/, 'Please enter a valid postal code'),
  }).optional(),
  
  special_instructions: z.string()
    .max(500, 'Special instructions must be less than 500 characters')
    .optional(),
});

// Order validation schema
export const orderSchema = z.object({
  customer: customerSchema,
  items: z.array(z.object({
    product_id: z.string(),
    quantity: z.number().min(1, 'Quantity must be at least 1'),
  })).min(1, 'Order must contain at least one item'),
  collection_id: z.string().optional(),
});

export type CustomerFormData = z.infer<typeof customerSchema>;
export type OrderFormData = z.infer<typeof orderSchema>;

// Validation helper functions
export const validateCustomer = (data: unknown) => {
  return customerSchema.safeParse(data);
};

export const validateOrder = (data: unknown) => {
  return orderSchema.safeParse(data);
};

// Phone number formatting
export const formatPhoneNumber = (phone: string): string => {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // Format Indian phone numbers
  if (cleaned.length === 10) {
    return `+91 ${cleaned.slice(0, 5)} ${cleaned.slice(5)}`;
  } else if (cleaned.length === 12 && cleaned.startsWith('91')) {
    return `+${cleaned.slice(0, 2)} ${cleaned.slice(2, 7)} ${cleaned.slice(7)}`;
  } else if (cleaned.length === 13 && cleaned.startsWith('91')) {
    return `+${cleaned.slice(0, 2)} ${cleaned.slice(2, 7)} ${cleaned.slice(7)}`;
  }
  
  return phone;
};

// Email validation helper
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

// Indian postal code validation
export const isValidIndianPostalCode = (postalCode: string): boolean => {
  const indianPostalCodeRegex = /^[1-9][0-9]{5}$/;
  return indianPostalCodeRegex.test(postalCode.replace(/\s/g, ''));
};

// Form field validation states
export interface FieldError {
  message: string;
  type: 'error' | 'warning';
}

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, FieldError>;
}

// Real-time field validation
export const validateField = (fieldName: string, value: any, schema: z.ZodSchema): FieldError | null => {
  try {
    schema.parse({ [fieldName]: value });
    return null;
  } catch (error) {
    if (error instanceof z.ZodError) {
      const fieldError = error.errors.find(err => err.path.includes(fieldName));
      if (fieldError) {
        return {
          message: fieldError.message,
          type: 'error'
        };
      }
    }
    return null;
  }
};
