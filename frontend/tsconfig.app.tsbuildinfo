{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/common/errorboundary.tsx", "./src/components/common/notificationcontainer.tsx", "./src/components/common/searchbar.tsx", "./src/components/layout/footer.tsx", "./src/components/layout/header.tsx", "./src/components/layout/layout.tsx", "./src/components/products/productcard.tsx", "./src/components/products/productfilters.tsx", "./src/components/products/productgrid.tsx", "./src/components/products/productimagegallery.tsx", "./src/contexts/cartcontext.tsx", "./src/contexts/notificationcontext.tsx", "./src/contexts/wishlistcontext.tsx", "./src/lib/api.ts", "./src/lib/validation.ts", "./src/pages/cartpage.tsx", "./src/pages/checkoutpage.tsx", "./src/pages/collectionpage.tsx", "./src/pages/collectionspage.tsx", "./src/pages/homepage.tsx", "./src/pages/notfoundpage.tsx", "./src/pages/orderconfirmationpage.tsx", "./src/pages/ordertrackingpage.tsx", "./src/pages/productpage.tsx", "./src/pages/searchpage.tsx", "./src/pages/wishlistpage.tsx", "./src/types/index.ts"], "version": "5.8.3"}