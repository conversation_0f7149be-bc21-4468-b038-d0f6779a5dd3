# If you prefer the allow list template instead of the deny list, see community template:
# https://github.com/github/gitignore/blob/main/community/Golang/Go.AllowList.gitignore
#
# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work
go.work.sum

# env file
.env

node_modules
dist-ssr
*.local

# Emacs undo-tree files
*.~undo-tree~
.aider*

# Additional IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Database
*.db
*.sqlite
*.sqlite3
postgres_data/

# Build outputs
build/
out/
coverage/
*.cover

# Environment files
.env.local
.env.development.local
.env.test.local
.env.production.local

# Temporary files
*.tmp
*.temp
*.bak
*.backup

# Docker/Podman
.dockerignore

# Local development
local/
tmp/
uploads/

# SSL certificates
*.pem
*.key
*.crt

# Backup files
*.orig
