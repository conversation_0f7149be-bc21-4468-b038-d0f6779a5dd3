# Anand Jewels - E-Commerce Platform

A curated jewelry showcase platform where admins upload items, create collections with shareable links, customers select items and place orders, and admins confirm orders via phone calls.

## Tech Stack

- **Backend**: Golang (Gin framework)
- **Frontend**: React + Vite + Chakra UI
- **Database**: PostgreSQL + Redis
- **Auth**: Google OAuth 2.0
- **Images**: Cloudinary
- **Containerization**: Podman

## Project Structure

```
├── backend/                 # Golang API server
│   ├── cmd/                # Application entry points
│   ├── internal/           # Private application code
│   │   ├── handlers/       # HTTP handlers
│   │   ├── models/         # Data models
│   │   ├── middleware/     # HTTP middleware
│   │   ├── services/       # Business logic
│   │   ├── config/         # Configuration
│   │   └── database/       # Database connection
│   ├── pkg/               # Public packages
│   └── migrations/        # Database migrations
├── frontend/              # React application
│   └── src/
│       ├── components/    # Reusable components
│       ├── pages/         # Page components
│       ├── hooks/         # Custom React hooks
│       ├── services/      # API services
│       ├── utils/         # Utility functions
│       ├── contexts/      # React contexts
│       └── assets/        # Static assets
├── database/              # Database schema and scripts
├── docs/                  # Documentation
└── scripts/               # Build and deployment scripts
```

## Quick Start

### Prerequisites

- Go 1.21+
- Node.js 18+
- PostgreSQL 15+
- Redis 7+
- Podman

### Development Setup

#### Using Makefile (Recommended)

1. Clone the repository
2. Copy environment files:
   ```bash
   cp backend/.env.example backend/.env
   cp frontend/.env.example frontend/.env
   ```
3. Update environment variables with your values
4. Start all services:
   ```bash
   make start
   ```
5. Check status:
   ```bash
   make status
   ```
6. View all available commands:
   ```bash
   make help
   ```

#### Manual Setup

1. Clone the repository
2. Copy environment files:
   ```bash
   cp backend/.env.example backend/.env
   cp frontend/.env.example frontend/.env
   ```
3. Update environment variables with your values
4. Start development environment:
   ```bash
   podman compose up -d
   ```

#### Available URLs
- Frontend: http://localhost:5173
- Admin Portal: http://localhost:5173 (same port, different container)
- Backend API: http://localhost:8080
- MinIO Console: http://localhost:9001
- PostgreSQL: localhost:5432
- Redis: localhost:6379

### Backend Development

```bash
cd backend
go mod tidy
go run cmd/main.go
```

### Frontend Development

```bash
cd frontend
npm install
npm run dev
```

## Features

### Admin Portal
- **Unified Search**: Search across products, collections, customers, and orders from any page
- Image upload with compression and optimization
- Collection creation and management
- Order management dashboard
- Inventory tracking
- Real-time search with keyboard navigation
- Dedicated search page with advanced filtering

### Customer Interface
- Browse collections via shareable URLs
- Shopping cart functionality
- Order placement (no payment processing)
- Responsive design for mobile/desktop

## API Documentation

API documentation will be available at `/api/docs` when the server is running.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is proprietary software for Anand Jewels.