# Docker Compose file for testing Kubernetes-ready images locally
version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    image: anand-jewels/backend:latest
    ports:
      - "8080:8080"
    environment:
      - PORT=8080
      - GIN_MODE=release
      - DATABASE_URL=**********************************************/jewelry
      - REDIS_URL=redis://:redispass@redis:6379
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin123
      - MINIO_BUCKET_NAME=jewelry-images
      - MINIO_USE_SSL=false
      - API_BASE_URL=http://localhost:8080
      - JWT_SECRET=your-jwt-secret-key
    depends_on:
      - postgres
      - redis
      - minio
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 3s
      retries: 3
      start_period: 5s

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    image: anand-jewels/frontend:latest
    ports:
      - "3000:8080"
    environment:
      - VITE_API_URL=http://localhost:8080/api/v1
      - VITE_APP_NAME=Anand Jewels
      - VITE_DEBUG_MODE=false
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 3s
      retries: 3
      start_period: 5s

  admin-portal:
    build:
      context: ./admin-portal
      dockerfile: Dockerfile
    image: anand-jewels/admin-portal:latest
    ports:
      - "3001:8080"
    environment:
      - VITE_API_URL=http://localhost:8080/api/v1
      - VITE_APP_NAME=Anand Jewels Admin
      - VITE_DEBUG_MODE=false
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 3s
      retries: 3
      start_period: 5s

  postgres:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=jewelry
      - POSTGRES_USER=jewelry
      - POSTGRES_PASSWORD=jewelrypass
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U jewelry"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    command: redis-server --requirepass redispass
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  minio:
    image: minio/minio:latest
    command: minio server /data --console-address ":9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin123
    volumes:
      - minio_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

volumes:
  postgres_data:
  redis_data:
  minio_data:
